<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>AI文本处理器</title>

    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 20px;
            margin: 0;
        }

        .header p {
            color: #7f8c8d;
            margin: 5px 0 0 0;
            font-size: 12px;
        }

        .mode-section {
            margin: 20px 0;
        }

        .mode-tabs {
            display: flex;
            border-bottom: 2px solid #ecf0f1;
            margin-bottom: 20px;
        }

        .mode-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #7f8c8d;
            transition: all 0.3s;
        }

        .mode-tab.active {
            color: #3498db;
            border-bottom: 2px solid #3498db;
        }

        .mode-tab:hover {
            background-color: #f8f9fa;
        }

        .action-section {
            margin: 20px 0;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin: 10px 0;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-primary:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }

        .status.info {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .progress-section {
            display: none;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }

        .progress-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background-color: #3498db;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 14px;
            color: #7f8c8d;
        }

        .stream-output {
            display: none;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            max-height: 200px;
            overflow-y: auto;
        }

        .stream-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stream-content {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #495057;
            white-space: pre-wrap;
        }

        .preview-section {
            display: none;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .preview-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .preview-content {
            background-color: white;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            font-size: 14px;
            line-height: 1.5;
            color: #495057;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .preview-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .hint-section {
            display: none;
            margin: 20px 0;
            padding: 12px 15px;
            background-color: #e8f5e8;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            animation: fadeIn 0.5s ease-in;
        }

        .hint-content {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #155724;
        }

        .hint-icon {
            font-size: 18px;
            margin-right: 8px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .controls-section {
            margin: 20px 0;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-secondary:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .instructions {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 10px;
            margin: 20px 0;
        }

        .instructions h3 {
            margin: 0 0 10px 0;
            color: #0c5460;
            font-size: 14px;
        }

        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin: 5px 0;
            color: #0c5460;
            font-size: 13px;
        }

        .custom-input-section {
            margin: 20px 0;
        }

        .custom-input {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            resize: vertical;
            box-sizing: border-box;
        }

        .custom-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .custom-input::placeholder {
            color: #7f8c8d;
        }
    </style>
</head>

<body>
    <div class="container">
        <div id="header" class="header">
            <!-- 动态生成的标题 -->
        </div>

        <div class="mode-section">
            <div id="mode-tabs" class="mode-tabs">
                <!-- 动态生成的标签页 -->
            </div>
        </div>

        <div id="custom-input-section" class="custom-input-section" style="display: none;">
            <textarea id="custom-input" class="custom-input"></textarea>
        </div>

        <div id="instructions" class="instructions">
            <!-- 动态生成的使用说明 -->
        </div>

        <div class="action-section">
            <button id="process-button" class="btn btn-primary" onclick="processSelectedText()">
                🚀 <span id="button-text">处理文本</span>
            </button>
        </div>

        <div id="progress-section" class="progress-section">
            <div class="progress-title">处理进度</div>
            <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
            <div id="progress-text" class="progress-text">准备中...</div>
        </div>

        <div id="stream-output" class="stream-output">
            <div class="stream-title">实时输出</div>
            <div id="stream-content" class="stream-content"></div>
        </div>

        <div id="preview-section" class="preview-section">
            <div class="preview-title">
                📝 生成结果预览
            </div>
            <div id="preview-content" class="preview-content"></div>
            <div class="preview-actions">
                <button id="confirm-button" class="btn btn-primary" onclick="confirmChanges()">
                    ✅ 确认修改
                </button>
                <button id="cancel-button" class="btn btn-secondary" onclick="cancelChanges()">
                    ❌ 取消
                </button>
            </div>
        </div>

        <div id="hint-section" class="hint-section">
            <div class="hint-content">
                <span class="hint-icon">💡</span>
                <span id="hint-text">修改已应用到文档，在文档中按 Ctrl+Z 可撤销此次修改</span>
            </div>
        </div>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <!-- JavaScript -->
    <script type="text/javascript" src="taskpane.js"></script>
</body>

</html>

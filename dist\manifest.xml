<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1" 
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
           xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0" 
           xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" 
           xsi:type="TaskPaneApp">

  <!-- Begin Basic Settings: Add-in metadata, used for all versions of Office unless override provided. -->
  <Id>12345678-1234-1234-1234-123456789012</Id>
  <Version>*******</Version>
  <ProviderName>车管所张硕创造</ProviderName>
  <DefaultLocale>zh-CN</DefaultLocale>
  <DisplayName DefaultValue="模型优化器" />
  <Description DefaultValue="使用模型优化Word文档中的选中文本" />
  <IconUrl DefaultValue="https://www.contoso.com/assets/icon-32.png"/>
  <HighResolutionIconUrl DefaultValue="https://www.contoso.com/assets/icon-64.png"/>

  <!-- End Basic Settings. -->

  <!-- Begin TaskPane Mode integration. This section is used if there are no VersionOverrides or if the Office client version does not support add-in commands. -->
  <Hosts>
    <Host Name="Document" />
  </Hosts>
  <DefaultSettings>
    <SourceLocation DefaultValue="https://www.contoso.com/taskpane.html"/>
  </DefaultSettings>
  <Permissions>ReadWriteDocument</Permissions>
  <!-- End TaskPane Mode integration.  -->

  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">

    <!-- The Hosts node is required. -->
    <Hosts>
      <!-- Each host can have a different set of commands. -->
      <!-- Workbook=Excel Document=Word Presentation=PowerPoint -->
      <!-- Make sure the hosts you override match the hosts declared in the top level <Hosts> element. -->
      <Host xsi:type="Document">
        <!-- Form factor. Currently only DesktopFormFactor is supported. -->
        <DesktopFormFactor>
          <!--"This code enables a customizable message to be displayed when the add-in is loaded successfully upon individual install."-->
          <GetStarted>
            <!-- Title of the Getting Started callout. resid points to a ShortString resource -->
            <Title resid="GetStarted.Title"/>
            <!-- Description of the Getting Started callout. resid points to a LongString resource -->
            <Description resid="GetStarted.Description"/>
            <!-- Point to a url resource which details how the add-in should be used. -->
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
          </GetStarted>
          <!-- Function file is a HTML page that includes the JavaScript where functions for ExecuteAction will be called. 
               Think of the FunctionFile as the code behind ExecuteFunction. -->
          <FunctionFile resid="Commands.Url" />

          <!-- PrimaryCommandSurface is the main Office Ribbon. -->
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <!-- Use OfficeTab to extend an existing Tab. Use CustomTab to create a new tab. -->
            <OfficeTab id="TabHome">
              <!-- Ensure you provide a unique id for the group. Recommendation for any IDs is to namespace using your company name. -->
              <Group id="DeepSeekGroup">
                <!-- Label for your group. resid must point to a ShortString resource. -->
                <Label resid="DeepSeekGroup.Label" />
                <!-- Icons. Required sizes 16,32,80, optional 20, 24, 40, 48, 64. Strongly recommended to provide all sizes for great UX. -->
                <!-- Use PNG icons. All URLs on the resources section must use HTTPS. -->
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16" />
                  <bt:Image size="32" resid="Icon.32x32" />
                  <bt:Image size="80" resid="Icon.80x80" />
                </Icon>

                <!-- Control. It can be of type "Button" or "Menu". -->
                <Control xsi:type="Button" id="OptimizeTextButton">
                  <Label resid="OptimizeTextButton.Label" />
                  <Supertip>
                    <!-- ToolTip title. resid must point to a ShortString resource. -->
                    <Title resid="OptimizeTextButton.Label" />
                    <!-- ToolTip description. resid must point to a LongString resource. -->
                    <Description resid="OptimizeTextButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>

                  <!-- This is what happens when the command is triggered (E.g. click on the Ribbon). Supported actions are ExecuteFunction or ShowTaskpane. -->
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url"/>
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>

    <!-- You can use resources across hosts and form factors. -->
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://www.contoso.com/assets/icon-16.png"/>
        <bt:Image id="Icon.32x32" DefaultValue="https://www.contoso.com/assets/icon-32.png"/>
        <bt:Image id="Icon.80x80" DefaultValue="https://www.contoso.com/assets/icon-80.png"/>
      </bt:Images>
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://go.microsoft.com/fwlink/?LinkId=276812" />
        <bt:Url id="Commands.Url" DefaultValue="https://www.contoso.com/commands.html" />
        <bt:Url id="Taskpane.Url" DefaultValue="https://www.contoso.com/taskpane.html" />
      </bt:Urls>
      <!-- ShortStrings max characters==125. -->
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="开始使用模型优化器!" />
        <bt:String id="DeepSeekGroup.Label" DefaultValue="AI文本优化" />
        <bt:String id="OptimizeTextButton.Label" DefaultValue="使用模型优化" />
      </bt:ShortStrings>
      <!-- LongStrings max characters==250. -->
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="您的加载项已成功加载。选择文本并点击'使用模型优化'按钮来改善您的文本质量。" />
        <bt:String id="OptimizeTextButton.Tooltip" DefaultValue="选择文本后点击此按钮，使用模型优化优化您的文本内容。" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>

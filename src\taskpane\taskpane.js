/*
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * See LICENSE in the project root for license information.
 */

/* global document, Office, Word */

// 全局变量
let config = null; // 配置对象
let currentMode = 'optimize'; // 当前选择的模式
let originalText = ''; // 保存原始文本
let generatedText = ''; // 保存AI生成的文本
let currentSelection = null; // 保存当前选择的文本范围



// 等待Office.js加载完成
function waitForOffice() {
  return new Promise((resolve) => {
    if (typeof Office !== 'undefined' && Office.onReady) {
      resolve();
    } else {
      // 等待Office.js加载
      const checkOffice = () => {
        if (typeof Office !== 'undefined' && Office.onReady) {
          resolve();
        } else {
          setTimeout(checkOffice, 100);
        }
      };
      setTimeout(checkOffice, 100);
    }
  });
}

// 初始化函数
async function initialize() {
  console.log("Starting initialization...");

  try {
    // 等待Office.js加载（最多等待5秒）
    await Promise.race([
      waitForOffice(),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Office.js timeout')), 5000))
    ]);

    console.log("Office.js loaded, calling Office.onReady...");

    Office.onReady((info) => {
      console.log("Office.onReady called, info:", info);

      // 等待DOM完全加载后初始化应用
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
      } else {
        initializeApp();
      }
    }).catch(error => {
      console.error("Office.onReady error:", error);
      initializeAppFallback();
    });

  } catch (error) {
    console.warn("Office.js not available or timeout:", error);
    initializeAppFallback();
  }
}

// 备用初始化（浏览器环境）
function initializeAppFallback() {
  console.log("Fallback: initializing app without Office context...");
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
  } else {
    initializeApp();
  }
}

// 开始初始化
initialize();

/**
 * 初始化应用
 */
async function initializeApp() {
  console.log("Initializing app...");

  try {
    // 检查Office环境
    console.log("Office object:", typeof Office !== 'undefined' ? Office : 'undefined');
    console.log("Word object:", typeof Word !== 'undefined' ? Word : 'undefined');

    // 加载配置
    await loadConfig();

    // 初始化UI
    initializeUI();

    // 绑定事件
    bindEvents();
  } catch (error) {
    console.error("Failed to initialize app:", error);

    // 显示错误状态
    try {
      const statusElement = document.getElementById("status");
      if (statusElement) {
        statusElement.textContent = `❌ 应用初始化失败: ${error.message}`;
        statusElement.className = "status error";
        statusElement.style.display = "block";
      }

      // 隐藏其他元素
      const elementsToHide = ["mode-tabs", "custom-input-section", "instructions", "process-button"];
      elementsToHide.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          element.style.display = "none";
        }
      });
    } catch (statusError) {
      console.error("Failed to show status:", statusError);
    }
  }
}

/**
 * 加载配置文件
 */
async function loadConfig() {
  // 尝试多个可能的配置文件路径
  const possiblePaths = [
    './conf.json',
    'conf.json',
    '../conf.json',
    '/conf.json'
  ];

  let lastError = null;

  for (const configPath of possiblePaths) {
    try {
      const response = await fetch(configPath);

      if (response.ok) {
        const configText = await response.text();
        config = JSON.parse(configText);

        // 验证配置结构
        if (!config.sections || !Array.isArray(config.sections) || config.sections.length === 0) {
          throw new Error('Invalid config: sections is empty or not an array');
        }

        console.log(`✅ Config loaded: ${config.sections.length} sections from ${configPath}`);
        return; // 成功加载，退出函数
      } else {
        lastError = new Error(`HTTP ${response.status} for ${configPath}`);
      }
    } catch (error) {
      lastError = error;
    }
  }

  // 如果所有路径都失败了
  console.error("❌ Failed to load config from all paths");
  throw new Error(`配置文件加载失败: ${lastError?.message || '所有路径都无法访问'}`);
}



/**
 * 初始化UI
 */
function initializeUI() {
  if (!config) {
    console.error("❌ Config is null or undefined!");
    return;
  }

  if (!config.sections || !Array.isArray(config.sections)) {
    console.error("❌ Config sections is not an array:", config.sections);
    return;
  }

  // 设置标题
  const header = document.getElementById("header");
  if (header && config.app) {
    header.innerHTML = `
      <h1>${config.app.title}</h1>
      <p>${config.app.subtitle}</p>
    `;
  } else {
    console.error("❌ Header element not found or app config missing");
  }

  // 生成模式标签页
  const modeTabs = document.getElementById("mode-tabs");
  if (!modeTabs) {
    console.error("❌ mode-tabs element not found");
    return;
  }

  console.log("🏷️ Generating tabs for sections:", config.sections.map(s => s.name));
  modeTabs.innerHTML = '';

  config.sections.forEach((section, index) => {
    console.log(`🏷️ Creating tab ${index + 1}: ${section.name} (${section.id})`);

    const tab = document.createElement('button');
    tab.id = `${section.id}-tab`;
    tab.className = `mode-tab ${index === 0 ? 'active' : ''}`;
    tab.textContent = section.name;
    tab.onclick = () => {
      switchMode(section.id);
    };
    modeTabs.appendChild(tab);
  });

  console.log(`✅ Created ${config.sections.length} tabs`);

  // 设置默认模式
  if (config.sections.length > 0) {
    currentMode = config.sections[0].id;
    console.log(`🎯 Default mode set to: ${currentMode}`);
  }

  // 生成使用说明
  const instructions = document.getElementById("instructions");
  if (instructions && config.ui?.instructions) {
    const instructionsConfig = config.ui.instructions;
    instructions.innerHTML = `
      <h3>${instructionsConfig.title}</h3>
      <ol>
        ${instructionsConfig.steps.map(step => `<li>${step}</li>`).join('')}
      </ol>
    `;
  } else {
    console.warn("⚠️ Instructions element or config not found");
    if (instructions) {
      instructions.style.display = "none";
    }
  }

  // 更新按钮文本
  updateButtonText();
}

/**
 * 绑定所有事件处理器
 */
function bindEvents() {
  // 绑定处理按钮
  const processButton = document.getElementById("process-button");
  if (processButton) {
    processButton.onclick = processSelectedText;
  } else {
    console.error("Process button not found");
  }
}

/**
 * 切换处理模式
 * @param {string} mode - 模式ID
 */
function switchMode(mode) {
  currentMode = mode;

  // 更新标签页样式
  document.querySelectorAll('.mode-tab').forEach(tab => {
    tab.classList.remove('active');
  });

  const activeTab = document.getElementById(`${mode}-tab`);
  if (activeTab) {
    activeTab.classList.add('active');
    console.log(`Added active class to tab: ${activeTab.id}`);
  }

  // 更新按钮文本
  updateButtonText();

  // 显示或隐藏自定义输入框
  const currentSection = getCurrentSection();
  const customInputSection = document.getElementById('custom-input-section');

  if (currentSection && currentSection.showCustomInput) {
    customInputSection.style.display = 'block';
    const customInput = document.getElementById('custom-input');
    if (customInput && currentSection.customInputPlaceholder) {
      customInput.placeholder = currentSection.customInputPlaceholder;
    }
  } else {
    customInputSection.style.display = 'none';
  }
}

/**
 * 更新按钮文本
 */
function updateButtonText() {
  const buttonText = document.getElementById('button-text');
  const currentSection = getCurrentSection();

  if (buttonText && currentSection) {
    buttonText.textContent = currentSection.buttonText;
    console.log(`Updated button text to: ${currentSection.buttonText}`);
  }
}

/**
 * 获取当前选择的配置段
 */
function getCurrentSection() {
  if (!config || !config.sections) return null;
  return config.sections.find(section => section.id === currentMode);
}



// 将函数添加到全局作用域
window.switchMode = switchMode;

/**
 * 处理选中的文本（优化或扩写）
 */
async function processSelectedText() {
  console.log("processSelectedText called, current mode:", currentMode);

  const button = document.getElementById("process-button");
  const progressSection = document.getElementById("progress-section");
  const streamOutput = document.getElementById("stream-output");
  const previewSection = document.getElementById("preview-section");
  const hintSection = document.getElementById("hint-section");
  const status = document.getElementById("status");

  let success = false;
  const startTime = Date.now();

  try {
    // 隐藏之前的结果和提示
    previewSection.style.display = "none";
    hintSection.style.display = "none";
    status.style.display = "none";

    // 显示处理状态
    button.disabled = true;
    progressSection.style.display = "block";
    streamOutput.style.display = "block";

    // 重置进度和输出
    updateProgress(0, "准备中...");
    clearStreamOutput();

    // 检查是否在Office环境中
    if (typeof Word !== 'undefined' && Word.run) {
      // Office环境中的处理
      await Word.run(async (context) => {
        // 获取选中的文本
        const selection = context.document.getSelection();
        selection.load("text");

        await context.sync();

        // 验证选中的文本
        if (!validateText(selection.text)) {
          const messages = config?.ui?.messages || {};
          throw new Error(messages.noSelection || "请先选择有效的文本内容");
        }

        // 保存原始文本和选择范围
        originalText = selection.text;
        currentSelection = selection;

        updateProgress(20, "开始处理文本...");

        // 根据模式调用不同的API
        const apiResult = await callModelAPI(selection.text, currentMode);

        if (apiResult && apiResult !== selection.text) {
          updateProgress(100, "生成完成！");

          // 保存生成的文本
          generatedText = apiResult;

          // 隐藏进度和流式输出
          progressSection.style.display = "none";
          streamOutput.style.display = "none";

          // 显示预览
          showPreview(generatedText);

          const duration = Date.now() - startTime;
          const messages = config?.ui?.messages || {};
          const currentSection = getCurrentSection();
          const actionText = currentSection?.name || '处理';
          showStatus(`✅ ${actionText}${messages.success || '完成！'}(耗时: ${Math.round(duration/1000)}秒)`, "info");

          success = true;
        } else {
          updateProgress(100, "无需处理");
          showStatus("⚠️ 文本无需处理或AI返回相同内容", "warning");
          success = true;
        }
      });
    } else {
      // 浏览器环境中的模拟处理
      console.log("Running in browser environment, simulating processing...");

      const testText = "这是一个测试文本，用于演示功能。";
      originalText = testText;
      updateProgress(20, "开始处理文本...");

      // 模拟API调用
      setTimeout(async () => {
        try {
          const apiResult = await callModelAPI(testText, currentMode);

          updateProgress(100, "生成完成！");

          // 保存生成的文本
          generatedText = apiResult;

          // 隐藏进度和流式输出
          progressSection.style.display = "none";
          streamOutput.style.display = "none";

          // 显示预览
          showPreview(generatedText);

          const duration = Date.now() - startTime;
          const messages = config?.ui?.messages || {};
          const currentSection = getCurrentSection();
          const actionText = currentSection?.name || '处理';
          showStatus(`✅ ${actionText}${messages.success || '完成！'}(耗时: ${Math.round(duration/1000)}秒)`, "info");

          success = true;

        } catch (error) {
          console.error("API调用失败:", error);
          showStatus(`❌ API调用失败: ${error.message}`, "error");
          updateProgress(0, "处理失败");
        }
      }, 1000);
    }

  } catch (error) {
    console.error("处理文本时出错:", error);

    // 提供更友好的错误消息
    let errorMessage = error.message;
    const messages = config?.ui?.messages || {};

    if (error.message.includes("网络")) {
      errorMessage = messages.networkError || error.message;
    } else if (error.message.includes("API")) {
      errorMessage = messages.apiError || error.message;
    } else if (error.message.includes("选择")) {
      errorMessage = messages.noSelection || error.message;
    } else if (error.message.includes("配置")) {
      errorMessage = error.message;
    }

    showStatus(`❌ ${errorMessage}`, "error");
    updateProgress(0, "处理失败");
  } finally {
    // 恢复按钮状态
    button.disabled = false;

    // 记录使用统计
    logUsage(`${currentMode}_text`, success);
  }
}

/**
 * 显示预览
 * @param {string} text - 要预览的文本
 */
function showPreview(text) {
  const previewSection = document.getElementById("preview-section");
  const previewContent = document.getElementById("preview-content");

  previewContent.textContent = text;
  previewSection.style.display = "block";
}

/**
 * 确认修改 - 将生成的文本应用到Word文档
 */
async function confirmChanges() {
  if (!generatedText || !currentSelection) {
    showStatus("❌ 没有可确认的更改", "error");
    return;
  }

  try {
    if (typeof Word !== 'undefined' && Word.run) {
      // Office环境中应用更改
      await Word.run(async (context) => {
        // 重新获取选择范围（因为可能已经失效）
        const selection = context.document.getSelection();
        selection.load("text");
        await context.sync();

        // 如果当前选择的文本与原始文本匹配，则替换
        if (selection.text && selection.text.trim() === originalText.trim()) {
          selection.insertText(generatedText, Word.InsertLocation.replace);
          await context.sync();
        } else {
          // 如果选择已经改变，尝试搜索原始文本并替换
          const searchResults = context.document.body.search(originalText.substring(0, 50), {
            matchCase: false,
            matchWholeWord: false
          });

          searchResults.load('items');
          await context.sync();

          if (searchResults.items.length > 0) {
            searchResults.items[0].insertText(generatedText, Word.InsertLocation.replace);
            await context.sync();
          } else {
            throw new Error("无法找到要替换的原始文本");
          }
        }

        // 隐藏预览，显示提示
        document.getElementById("preview-section").style.display = "none";
        showHint();

        const messages = config?.ui?.messages || {};
        showStatus(`✅ ${messages.applied || '修改已应用到文档'}`, "info");
      });
    } else {
      // 浏览器环境中的模拟
      document.getElementById("preview-section").style.display = "none";
      showHint();
      const messages = config?.ui?.messages || {};
      showStatus(`✅ ${messages.applied || '修改已应用到文档'}（模拟）`, "info");
    }
  } catch (error) {
    console.error("确认更改时出错:", error);
    showStatus(`❌ 应用更改失败: ${error.message}`, "error");
  }
}

/**
 * 取消修改 - 隐藏预览
 */
function cancelChanges() {
  document.getElementById("preview-section").style.display = "none";

  // 清空保存的数据
  generatedText = '';
  originalText = '';
  currentSelection = null;

  const messages = config?.ui?.messages || {};
  showStatus(`❌ ${messages.cancelled || '已取消修改'}`, "info");
}

/**
 * 显示提示
 */
function showHint() {
  const hintSection = document.getElementById("hint-section");
  const hintText = document.getElementById("hint-text");

  if (hintText) {
    const messages = config?.ui?.messages || {};
    hintText.textContent = messages.undoHint || '修改已应用到文档，在文档中按 Ctrl+Z 可撤销此次修改';
  }

  hintSection.style.display = "block";

  // 提示保持显示，不自动消失
}

// 将函数添加到全局作用域
window.processSelectedText = processSelectedText;
window.confirmChanges = confirmChanges;
window.cancelChanges = cancelChanges;



/**
 * 更新进度显示
 * @param {number} percent - 进度百分比 (0-100)
 * @param {string} text - 进度文本
 */
function updateProgress(percent, text) {
  const progressFill = document.getElementById("progress-fill");
  const progressText = document.getElementById("progress-text");

  progressFill.style.width = `${percent}%`;
  progressText.textContent = text;
}

/**
 * 清空流式输出
 */
function clearStreamOutput() {
  const streamContent = document.getElementById("stream-content");
  streamContent.textContent = '';
}



/**
 * 添加流式输出内容
 * @param {string} content - 要添加的内容
 */
function appendStreamOutput(content) {
  const streamContent = document.getElementById("stream-content");
  streamContent.textContent += content;

  // 自动滚动到底部
  const streamOutput = document.getElementById("stream-output");
  streamOutput.scrollTop = streamOutput.scrollHeight;
}

/**
 * 调用模型API处理文本
 * @param {string} text - 要处理的文本
 * @param {string} mode - 处理模式ID
 * @returns {Promise<string>} - 处理后的文本
 */
async function callModelAPI(text, mode) {
  try {
    updateProgress(30, "正在生成内容...");

    // 获取当前模式的配置
    const currentSection = getCurrentSection();
    if (!currentSection) {
      throw new Error(`未找到模式配置: ${mode}`);
    }

    const apiConfig = currentSection.api;
    let systemPrompt = currentSection.systemPrompt;

    // API请求调试信息
    console.log(`🚀 API Request - Mode: ${mode}`);
    console.log(`🔗 API URL: ${apiConfig.url}`);
    console.log(`🤖 Model: ${apiConfig.model}`);
    console.log(`🌡️ Temperature: ${apiConfig.temperature}`);
    console.log(`📝 Max Tokens: ${apiConfig.maxTokens}`);
    console.log(`📄 Input Text Length: ${text.length} chars`);

    // 构建用户消息
    let userMessage;
    if (currentSection.showCustomInput) {
      // 自定义模式，获取用户输入的要求
      const customInput = document.getElementById('custom-input');
      const customRequirement = customInput ? customInput.value.trim() : '';

      if (!customRequirement) {
        const messages = config?.ui?.messages || {};
        throw new Error(messages.customInputRequired || '请输入自定义处理要求');
      }

      userMessage = `${customRequirement}\n\n要处理的文本：\n${text}`;
      console.log(`🎯 Custom Requirement: ${customRequirement}`);
    } else {
      // 预设模式，直接使用配置的提示词
      userMessage = text;
    }

    console.log(`💬 System Prompt: ${systemPrompt.substring(0, 100)}...`);
    console.log(`👤 User Message Length: ${userMessage.length} chars`);

    // 构建请求体 - 根据API类型选择不同格式
    let requestBody;
    if (apiConfig.url.includes('/api/chat-messages') || apiConfig.apiType === 'dify') {
      // Dify API格式 - 根据真实请求格式
      requestBody = {
        response_mode: "streaming",
        conversation_id: "",
        query: userMessage,
        inputs: {}
      };
    } else {
      // OpenAI API格式
      requestBody = {
        model: apiConfig.model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userMessage
          }
        ],
        temperature: apiConfig.temperature,
        max_tokens: apiConfig.maxTokens,
        stream: apiConfig.responseMode === 'streaming'
      };
    }

    console.log(`📤 Sending API request...`);
    const startTime = Date.now();

    // 构建请求头 - 根据API类型设置
    const headers = {
      'Authorization': `Bearer ${apiConfig.bearerToken}`,
      'Content-Type': 'application/json'
    };

    const response = await fetch(apiConfig.url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    const responseTime = Date.now() - startTime;
    console.log(`📡 API Response - Status: ${response.status}, Time: ${responseTime}ms`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API Error Response: ${errorText}`);
      throw new Error(`API请求失败 (${response.status}): ${errorText}`);
    }

    updateProgress(40, "正在接收数据...");

    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let result = '';
    let buffer = '';
    let chunkCount = 0;
    let totalBytes = 0;

    console.log(`📥 Starting stream processing...`);

    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      chunkCount++;
      totalBytes += value.length;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop(); // 保留最后一个可能不完整的行

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);

          if (data === '[DONE]') {
            console.log(`🏁 Stream completed - Chunks: ${chunkCount}, Bytes: ${totalBytes}, Result length: ${result.length}`);
            break;
          }

          try {
            const parsed = JSON.parse(data);
            let content = '';

            // 检测API类型并解析相应格式
            if (parsed.event === 'message' && parsed.answer) {
              // Dify API格式 - message事件
              content = parsed.answer;
            } else if (parsed.event === 'message_replace' && parsed.answer) {
              // Dify API格式 - message_replace事件（替换之前的内容）
              result = parsed.answer; // 直接替换而不是追加
              clearStreamOutput();
              appendStreamOutput(result);
              updateProgress(Math.min(50 + (result.length / text.length) * 30, 80), "正在生成内容...");
              return; // 跳过后续处理
            } else if (parsed.event === 'message_end') {
              // Dify API格式 - 消息结束
              console.log('✅ Dify message completed');
              return;
            } else if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
              // OpenAI API格式
              content = parsed.choices[0].delta.content;
            }

            if (content) {
              result += content;
              appendStreamOutput(content);

              // 更新进度
              const progress = Math.min(50 + (result.length / text.length) * 30, 80);
              updateProgress(progress, "正在生成内容...");
            }
          } catch (e) {
            // 忽略解析错误，继续处理下一行
            console.warn('❌ Stream parsing error:', e.message, 'Data:', data);
          }
        }
      }
    }

    if (result.trim()) {
      // 移除可能的引号包装
      let finalResult = result.trim();
      if ((finalResult.startsWith('"') && finalResult.endsWith('"')) ||
          (finalResult.startsWith("'") && finalResult.endsWith("'"))) {
        finalResult = finalResult.slice(1, -1);
      }

      console.log(`✅ API Success - Output length: ${finalResult.length} chars`);
      console.log(`📊 Compression ratio: ${((text.length - finalResult.length) / text.length * 100).toFixed(1)}%`);

      return finalResult;
    } else {
      console.error(`❌ API returned empty content`);
      throw new Error('API返回空内容');
    }

  } catch (error) {
    console.error('❌ API Call Failed:', error.message);
    console.error('🔍 Error Details:', {
      name: error.name,
      message: error.message,
      stack: error.stack?.split('\n')[0]
    });

    const messages = config?.ui?.messages || {};

    // 如果是网络错误，尝试进行连接测试
    if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
      updateProgress(10, "检测网络连接...");

      try {
        // 简单的连接测试 - 使用当前配置的API地址
        const currentSection = getCurrentSection();
        let testUrl;
        if (currentSection?.api?.url?.includes('/api/chat-messages')) {
          // Dify API - 使用基础URL进行测试
          const baseUrl = currentSection.api.url.replace('/api/chat-messages', '');
          testUrl = `${baseUrl}/api/ping`;
        } else if (currentSection?.api?.url) {
          // OpenAI API
          testUrl = currentSection.api.url.replace('/chat/completions', '/models');
        } else {
          // 如果没有配置，跳过连接测试
          throw new Error('无法进行连接测试：未找到API配置');
        }

        const testResponse = await fetch(testUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${currentSection?.api?.bearerToken || ''}`
          }
        });

        if (!testResponse.ok) {
          throw new Error('服务器连接失败');
        }

        // 连接正常，重新抛出原始错误
        throw new Error('API调用失败，请重试');
      } catch (testError) {
        throw new Error(messages.networkError || '网络连接失败，请检查网络连接');
      }
    } else if (error.message.includes('401')) {
      throw new Error(messages.authError || 'API认证失败，请检查API密钥');
    } else if (error.message.includes('429')) {
      throw new Error(messages.rateLimitError || 'API调用频率过高，请稍后再试');
    } else {
      throw new Error(`${messages.apiError || 'API调用失败'}: ${error.message}`);
    }
  }
}

/**
 * 显示状态消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (info, warning, error)
 */
function showStatus(message, type = "info") {
  const status = document.getElementById("status");
  status.textContent = message;
  status.className = `status ${type}`;
  status.style.display = "block";

  // 如果是成功或警告消息，3秒后自动隐藏
  if (type === "info" || type === "warning") {
    setTimeout(() => {
      status.style.display = "none";
    }, 3000);
  }
}

/**
 * 验证文本内容
 * @param {string} text - 要验证的文本
 * @returns {boolean} - 是否有效
 */
function validateText(text) {
  const messages = config?.ui?.messages || {};
  const settings = config?.settings || {};

  if (!text || text.trim() === "") {
    return false;
  }

  // 检查文本长度（从配置读取最大长度）
  const maxLength = settings.maxTextLength || 5000;
  if (text.length > maxLength) {
    const message = messages.textTooLong || `文本过长，请选择少于${maxLength}字符的文本`;
    showStatus(`⚠️ ${message}`, "warning");
    return false;
  }

  // 检查是否只包含空白字符
  if (text.trim().length === 0) {
    const message = messages.emptyText || "请选择包含实际内容的文本";
    showStatus(`⚠️ ${message}`, "warning");
    return false;
  }

  return true;
}

/**
 * 记录使用统计
 * @param {string} action - 操作类型
 * @param {boolean} success - 是否成功
 */
function logUsage(action, success) {
  try {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      action,
      success,
      userAgent: navigator.userAgent
    };

    // 存储到本地存储（可选）
    const logs = JSON.parse(localStorage.getItem('deepseek-optimizer-logs') || '[]');
    logs.push(logEntry);

    // 只保留最近100条记录
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }

    localStorage.setItem('deepseek-optimizer-logs', JSON.stringify(logs));
  } catch (error) {
    console.warn('无法记录使用统计:', error);
  }
}

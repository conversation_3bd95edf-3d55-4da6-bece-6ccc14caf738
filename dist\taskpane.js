/*! For license information please see taskpane.js.LICENSE.txt */
!function(){function e(e,t){var o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=function(e,t){if(e){if("string"==typeof e)return n(e,t);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?n(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){o&&(e=o);var r=0,c=function(){};return{s:c,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:c}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,i=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return s=e.done,e},e:function(e){i=!0,a=e},f:function(){try{s||null==o.return||o.return()}finally{if(i)throw a}}}}function n(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=Array(n);t<n;t++)o[t]=e[t];return o}function t(){var e,n,r="function"==typeof Symbol?Symbol:{},c=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function s(t,r,c,a){var s=r&&r.prototype instanceof l?r:l,u=Object.create(s.prototype);return o(u,"_invoke",function(t,o,r){var c,a,s,l=0,u=r||[],d=!1,f={p:0,n:0,v:e,a:m,f:m.bind(e,4),d:function(n,t){return c=n,a=0,s=e,f.n=t,i}};function m(t,o){for(a=t,s=o,n=0;!d&&l&&!r&&n<u.length;n++){var r,c=u[n],m=f.p,p=c[2];t>3?(r=p===o)&&(s=c[(a=c[4])?5:(a=3,3)],c[4]=c[5]=e):c[0]<=m&&((r=t<2&&m<c[1])?(a=0,f.v=o,f.n=c[1]):m<p&&(r=t<3||c[0]>o||o>p)&&(c[4]=t,c[5]=o,f.n=p,a=0))}if(r||t>1)return i;throw d=!0,o}return function(r,u,p){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&m(u,p),a=u,s=p;(n=a<2?e:s)||!d;){c||(a?a<3?(a>1&&(f.n=-1),m(a,s)):f.n=s:f.v=s);try{if(l=2,c){if(a||(r="next"),n=c[r]){if(!(n=n.call(c,s)))throw TypeError("iterator result is not an object");if(!n.done)return n;s=n.value,a<2&&(a=0)}else 1===a&&(n=c.return)&&n.call(c),a<2&&(s=TypeError("The iterator does not provide a '"+r+"' method"),a=1);c=e}else if((n=(d=f.n<0)?s:t.call(o,f))!==i)break}catch(n){c=e,a=1,s=n}finally{l=1}}return{value:n,done:d}}}(t,c,a),!0),u}var i={};function l(){}function u(){}function d(){}n=Object.getPrototypeOf;var f=[][c]?n(n([][c]())):(o(n={},c,function(){return this}),n),m=d.prototype=l.prototype=Object.create(f);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,o(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e}return u.prototype=d,o(m,"constructor",d),o(d,"constructor",u),u.displayName="GeneratorFunction",o(d,a,"GeneratorFunction"),o(m),o(m,a,"Generator"),o(m,c,function(){return this}),o(m,"toString",function(){return"[object Generator]"}),(t=function(){return{w:s,m:p}})()}function o(e,n,t,r){var c=Object.defineProperty;try{c({},"",{})}catch(e){c=0}o=function(e,n,t,r){function a(n,t){o(e,n,function(e){return this._invoke(n,t,e)})}n?c?c(e,n,{value:t,enumerable:!r,configurable:!r,writable:!r}):e[n]=t:(a("next",0),a("throw",1),a("return",2))},o(e,n,t,r)}function r(e,n,t,o,r,c,a){try{var s=e[c](a),i=s.value}catch(e){return void t(e)}s.done?n(i):Promise.resolve(i).then(o,r)}function c(e){return function(){var n=this,t=arguments;return new Promise(function(o,c){var a=e.apply(n,t);function s(e){r(a,o,c,s,i,"next",e)}function i(e){r(a,o,c,s,i,"throw",e)}s(void 0)})}}var a=null,s="optimize",i="",l="",u=null;function d(){return new Promise(function(e){if("undefined"!=typeof Office&&Office.onReady)e();else{var n=function(){"undefined"!=typeof Office&&Office.onReady?e():setTimeout(n,100)};setTimeout(n,100)}})}function f(){return(f=c(t().m(function e(){var n;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:return console.log("Starting initialization..."),e.p=1,e.n=2,Promise.race([d(),new Promise(function(e,n){return setTimeout(function(){return n(new Error("Office.js timeout"))},5e3)})]);case 2:console.log("Office.js loaded, calling Office.onReady..."),Office.onReady(function(e){console.log("Office.onReady called, info:",e),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",p):p()}).catch(function(e){console.error("Office.onReady error:",e),m()}),e.n=4;break;case 3:e.p=3,n=e.v,console.warn("Office.js not available or timeout:",n),m();case 4:return e.a(2)}},e,null,[[1,3]])}))).apply(this,arguments)}function m(){console.log("Fallback: initializing app without Office context..."),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",p):p()}function p(){return g.apply(this,arguments)}function g(){return(g=c(t().m(function e(){var n,o;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:return console.log("Initializing app..."),e.p=1,console.log("Office object:","undefined"!=typeof Office?Office:"undefined"),console.log("Word object:","undefined"!=typeof Word?Word:"undefined"),e.n=2,y();case 2:h(),b(),e.n=4;break;case 3:e.p=3,o=e.v,console.error("Failed to initialize app:",o);try{(n=document.getElementById("status"))&&(n.textContent="❌ 应用初始化失败: ".concat(o.message),n.className="status error",n.style.display="block"),["mode-tabs","custom-input-section","instructions","process-button"].forEach(function(e){var n=document.getElementById(e);n&&(n.style.display="none")})}catch(e){console.error("Failed to show status:",e)}case 4:return e.a(2)}},e,null,[[1,3]])}))).apply(this,arguments)}function y(){return v.apply(this,arguments)}function v(){return(v=c(t().m(function e(){var n,o,r,c,s,i,l,u;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:o=null,r=0,c=["./conf.json","conf.json","../conf.json","/conf.json"];case 1:if(!(r<c.length)){e.n=10;break}return s=c[r],e.p=2,e.n=3,fetch(s);case 3:if(!(i=e.v).ok){e.n=6;break}return e.n=4,i.text();case 4:if(l=e.v,(a=JSON.parse(l)).sections&&Array.isArray(a.sections)&&0!==a.sections.length){e.n=5;break}throw new Error("Invalid config: sections is empty or not an array");case 5:return console.log("✅ Config loaded: ".concat(a.sections.length," sections from ").concat(s)),e.a(2);case 6:o=new Error("HTTP ".concat(i.status," for ").concat(s));case 7:e.n=9;break;case 8:e.p=8,u=e.v,o=u;case 9:r++,e.n=1;break;case 10:throw console.error("❌ Failed to load config from all paths"),new Error("配置文件加载失败: ".concat((null===(n=o)||void 0===n?void 0:n.message)||"所有路径都无法访问"));case 11:return e.a(2)}},e,null,[[2,8]])}))).apply(this,arguments)}function h(){var e;if(a)if(a.sections&&Array.isArray(a.sections)){var n=document.getElementById("header");n&&a.app?n.innerHTML="\n      <h1>".concat(a.app.title,"</h1>\n      <p>").concat(a.app.subtitle,"</p>\n    "):console.error("❌ Header element not found or app config missing");var t=document.getElementById("mode-tabs");if(t){console.log("🏷️ Generating tabs for sections:",a.sections.map(function(e){return e.name})),t.innerHTML="",a.sections.forEach(function(e,n){console.log("🏷️ Creating tab ".concat(n+1,": ").concat(e.name," (").concat(e.id,")"));var o=document.createElement("button");o.id="".concat(e.id,"-tab"),o.className="mode-tab ".concat(0===n?"active":""),o.textContent=e.name,o.onclick=function(){w(e.id)},t.appendChild(o)}),console.log("✅ Created ".concat(a.sections.length," tabs")),a.sections.length>0&&(s=a.sections[0].id,console.log("🎯 Default mode set to: ".concat(s)));var o=document.getElementById("instructions");if(o&&null!==(e=a.ui)&&void 0!==e&&e.instructions){var r=a.ui.instructions;o.innerHTML="\n      <h3>".concat(r.title,"</h3>\n      <ol>\n        ").concat(r.steps.map(function(e){return"<li>".concat(e,"</li>")}).join(""),"\n      </ol>\n    ")}else console.warn("⚠️ Instructions element or config not found"),o&&(o.style.display="none");k()}else console.error("❌ mode-tabs element not found")}else console.error("❌ Config sections is not an array:",a.sections);else console.error("❌ Config is null or undefined!")}function b(){var e=document.getElementById("process-button");e?e.onclick=I:console.error("Process button not found")}function w(e){s=e,document.querySelectorAll(".mode-tab").forEach(function(e){e.classList.remove("active")});var n=document.getElementById("".concat(e,"-tab"));n&&(n.classList.add("active"),console.log("Added active class to tab: ".concat(n.id))),k();var t=E(),o=document.getElementById("custom-input-section");if(t&&t.showCustomInput){o.style.display="block";var r=document.getElementById("custom-input");r&&t.customInputPlaceholder&&(r.placeholder=t.customInputPlaceholder)}else o.style.display="none"}function k(){var e=document.getElementById("button-text"),n=E();e&&n&&(e.textContent=n.buttonText,console.log("Updated button text to: ".concat(n.buttonText)))}function E(){return a&&a.sections?a.sections.find(function(e){return e.id===s}):null}function I(){return x.apply(this,arguments)}function x(){return x=c(t().m(function e(){var n,o,r,d,f,m,p,g,y,v,h,b;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:if(console.log("processSelectedText called, current mode:",s),n=document.getElementById("process-button"),o=document.getElementById("progress-section"),r=document.getElementById("stream-output"),d=document.getElementById("preview-section"),f=document.getElementById("hint-section"),m=document.getElementById("status"),p=!1,g=Date.now(),e.p=1,d.style.display="none",f.style.display="none",m.style.display="none",n.disabled=!0,o.style.display="block",r.style.display="block",A(0,"准备中..."),P(),"undefined"==typeof Word||!Word.run){e.n=3;break}return e.n=2,Word.run(function(){var e=c(t().m(function e(n){var c,d,f,m,y,v,h,b,w;return t().w(function(e){for(;;)switch(e.n){case 0:return(c=n.document.getSelection()).load("text"),e.n=1,n.sync();case 1:if(L(c.text)){e.n=2;break}throw f=(null===(d=a)||void 0===d||null===(d=d.ui)||void 0===d?void 0:d.messages)||{},new Error(f.noSelection||"请先选择有效的文本内容");case 2:return i=c.text,u=c,A(20,"开始处理文本..."),e.n=3,C(c.text,s);case 3:(m=e.v)&&m!==c.text?(A(100,"生成完成！"),l=m,o.style.display="none",r.style.display="none",T(l),v=Date.now()-g,h=(null===(y=a)||void 0===y||null===(y=y.ui)||void 0===y?void 0:y.messages)||{},b=E(),w=(null==b?void 0:b.name)||"处理",W("✅ ".concat(w).concat(h.success||"完成！","(耗时: ").concat(Math.round(v/1e3),"秒)"),"info"),p=!0):(A(100,"无需处理"),W("⚠️ 文本无需处理或AI返回相同内容","warning"),p=!0);case 4:return e.a(2)}},e)}));return function(n){return e.apply(this,arguments)}}());case 2:e.n=4;break;case 3:console.log("Running in browser environment, simulating processing..."),i="这是一个测试文本，用于演示功能。",A(20,"开始处理文本..."),setTimeout(c(t().m(function e(){var n,c,i,u,d,f,m;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,C("这是一个测试文本，用于演示功能。",s);case 1:c=e.v,A(100,"生成完成！"),l=c,o.style.display="none",r.style.display="none",T(l),i=Date.now()-g,u=(null===(n=a)||void 0===n||null===(n=n.ui)||void 0===n?void 0:n.messages)||{},d=E(),f=(null==d?void 0:d.name)||"处理",W("✅ ".concat(f).concat(u.success||"完成！","(耗时: ").concat(Math.round(i/1e3),"秒)"),"info"),p=!0,e.n=3;break;case 2:e.p=2,m=e.v,console.error("API调用失败:",m),W("❌ API调用失败: ".concat(m.message),"error"),A(0,"处理失败");case 3:return e.a(2)}},e,null,[[0,2]])})),1e3);case 4:e.n=6;break;case 5:e.p=5,b=e.v,console.error("处理文本时出错:",b),v=b.message,h=(null===(y=a)||void 0===y||null===(y=y.ui)||void 0===y?void 0:y.messages)||{},b.message.includes("网络")?v=h.networkError||b.message:b.message.includes("API")?v=h.apiError||b.message:b.message.includes("选择")?v=h.noSelection||b.message:b.message.includes("配置")&&(v=b.message),W("❌ ".concat(v),"error"),A(0,"处理失败");case 6:return e.p=6,n.disabled=!1,M("".concat(s,"_text"),p),e.f(6);case 7:return e.a(2)}},e,null,[[1,5,6,7]])})),x.apply(this,arguments)}function T(e){var n=document.getElementById("preview-section");document.getElementById("preview-content").textContent=e,n.style.display="block"}function O(){return O=c(t().m(function e(){var n,o,r;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:if(l&&u){e.n=1;break}return W("❌ 没有可确认的更改","error"),e.a(2);case 1:if(e.p=1,"undefined"==typeof Word||!Word.run){e.n=3;break}return e.n=2,Word.run(function(){var e=c(t().m(function e(n){var o,r,c,s;return t().w(function(e){for(;;)switch(e.n){case 0:return(r=n.document.getSelection()).load("text"),e.n=1,n.sync();case 1:if(!r.text||r.text.trim()!==i.trim()){e.n=3;break}return r.insertText(l,Word.InsertLocation.replace),e.n=2,n.sync();case 2:e.n=7;break;case 3:return(c=n.document.body.search(i.substring(0,50),{matchCase:!1,matchWholeWord:!1})).load("items"),e.n=4,n.sync();case 4:if(!(c.items.length>0)){e.n=6;break}return c.items[0].insertText(l,Word.InsertLocation.replace),e.n=5,n.sync();case 5:e.n=7;break;case 6:throw new Error("无法找到要替换的原始文本");case 7:document.getElementById("preview-section").style.display="none",S(),s=(null===(o=a)||void 0===o||null===(o=o.ui)||void 0===o?void 0:o.messages)||{},W("✅ ".concat(s.applied||"修改已应用到文档"),"info");case 8:return e.a(2)}},e)}));return function(n){return e.apply(this,arguments)}}());case 2:e.n=4;break;case 3:document.getElementById("preview-section").style.display="none",S(),o=(null===(n=a)||void 0===n||null===(n=n.ui)||void 0===n?void 0:n.messages)||{},W("✅ ".concat(o.applied||"修改已应用到文档","（模拟）"),"info");case 4:e.n=6;break;case 5:e.p=5,r=e.v,console.error("确认更改时出错:",r),W("❌ 应用更改失败: ".concat(r.message),"error");case 6:return e.a(2)}},e,null,[[1,5]])})),O.apply(this,arguments)}function S(){var e=document.getElementById("hint-section"),n=document.getElementById("hint-text");if(n){var t,o=(null===(t=a)||void 0===t||null===(t=t.ui)||void 0===t?void 0:t.messages)||{};n.textContent=o.undoHint||"修改已应用到文档，在文档中按 Ctrl+Z 可撤销此次修改"}e.style.display="block"}function A(e,n){var t=document.getElementById("progress-fill"),o=document.getElementById("progress-text");t.style.width="".concat(e,"%"),o.textContent=n}function P(){document.getElementById("stream-content").textContent=""}function B(e){document.getElementById("stream-content").textContent+=e;var n=document.getElementById("stream-output");n.scrollTop=n.scrollHeight}function C(e,n){return j.apply(this,arguments)}function j(){return(j=c(t().m(function n(o,r){var c,s,i,l,u,d,f,m,p,g,y,v,h,b,w,k,I,x,T,O,S,C,j,W,L,M,R,D,_,F,N,z,G,H,q,J,U,Z,$,K,Q,V,X;return t().w(function(n){for(;;)switch(n.p=n.n){case 0:if(n.p=0,A(30,"正在生成内容..."),c=E()){n.n=1;break}throw new Error("未找到模式配置: ".concat(r));case 1:if(s=c.api,i=c.systemPrompt,console.log("🚀 API Request - Mode: ".concat(r)),console.log("🔗 API URL: ".concat(s.url)),console.log("🤖 Model: ".concat(s.model)),console.log("🌡️ Temperature: ".concat(s.temperature)),console.log("📝 Max Tokens: ".concat(s.maxTokens)),console.log("📄 Input Text Length: ".concat(o.length," chars")),!c.showCustomInput){n.n=3;break}if(u=document.getElementById("custom-input"),d=u?u.value.trim():""){n.n=2;break}throw m=(null===(f=a)||void 0===f||null===(f=f.ui)||void 0===f?void 0:f.messages)||{},new Error(m.customInputRequired||"请输入自定义处理要求");case 2:l="".concat(d,"\n\n要处理的文本：\n").concat(o),console.log("🎯 Custom Requirement: ".concat(d)),n.n=4;break;case 3:l=o;case 4:return console.log("💬 System Prompt: ".concat(i.substring(0,100),"...")),console.log("👤 User Message Length: ".concat(l.length," chars")),p=s.url.includes("/api/chat-messages")||"dify"===s.apiType?{response_mode:"streaming",conversation_id:"",query:l,inputs:{}}:{model:s.model,messages:[{role:"system",content:i},{role:"user",content:l}],temperature:s.temperature,max_tokens:s.maxTokens,stream:"streaming"===s.responseMode},console.log("📤 Sending API request..."),g=Date.now(),y={Authorization:"Bearer ".concat(s.bearerToken),"Content-Type":"application/json"},n.n=5,fetch(s.url,{method:"POST",headers:y,body:JSON.stringify(p)});case 5:if(v=n.v,h=Date.now()-g,console.log("📡 API Response - Status: ".concat(v.status,", Time: ").concat(h,"ms")),v.ok){n.n=7;break}return n.n=6,v.text();case 6:throw b=n.v,console.error("❌ API Error Response: ".concat(b)),new Error("API请求失败 (".concat(v.status,"): ").concat(b));case 7:A(40,"正在接收数据..."),w=v.body.getReader(),k=new TextDecoder,I="",x="",T=0,O=0,console.log("📥 Starting stream processing...");case 8:return n.n=9,w.read();case 9:if(S=n.v,C=S.done,j=S.value,!C){n.n=10;break}return n.a(3,24);case 10:T++,O+=j.length,x+=k.decode(j,{stream:!0}),W=x.split("\n"),x=W.pop(),L=e(W),n.p=11,L.s();case 12:if((M=L.n()).done){n.n=20;break}if(!(R=M.value).startsWith("data: ")){n.n=19;break}if("[DONE]"!==(D=R.slice(6))){n.n=13;break}return console.log("🏁 Stream completed - Chunks: ".concat(T,", Bytes: ").concat(O,", Result length: ").concat(I.length)),n.a(3,20);case 13:if(n.p=13,_=JSON.parse(D),F="","message"!==_.event||!_.answer){n.n=14;break}F=_.answer,n.n=17;break;case 14:if("message_replace"!==_.event||!_.answer){n.n=15;break}return I=_.answer,P(),B(I),A(Math.min(50+I.length/o.length*30,80),"正在生成内容..."),n.a(2);case 15:if("message_end"!==_.event){n.n=16;break}return console.log("✅ Dify message completed"),n.a(2);case 16:_.choices&&_.choices[0]&&_.choices[0].delta&&_.choices[0].delta.content&&(F=_.choices[0].delta.content);case 17:F&&(I+=F,B(F),A(Math.min(50+I.length/o.length*30,80),"正在生成内容...")),n.n=19;break;case 18:n.p=18,Q=n.v,console.warn("❌ Stream parsing error:",Q.message,"Data:",D);case 19:n.n=12;break;case 20:n.n=22;break;case 21:n.p=21,V=n.v,L.e(V);case 22:return n.p=22,L.f(),n.f(22);case 23:n.n=8;break;case 24:if(!I.trim()){n.n=25;break}return((N=I.trim()).startsWith('"')&&N.endsWith('"')||N.startsWith("'")&&N.endsWith("'"))&&(N=N.slice(1,-1)),console.log("✅ API Success - Output length: ".concat(N.length," chars")),console.log("📊 Compression ratio: ".concat(((o.length-N.length)/o.length*100).toFixed(1),"%")),n.a(2,N);case 25:throw console.error("❌ API returned empty content"),new Error("API返回空内容");case 26:n.n=39;break;case 27:if(n.p=27,X=n.v,console.error("❌ API Call Failed:",X.message),console.error("🔍 Error Details:",{name:X.name,message:X.message,stack:null===(z=X.stack)||void 0===z?void 0:z.split("\n")[0]}),H=(null===(G=a)||void 0===G||null===(G=G.ui)||void 0===G?void 0:G.messages)||{},!X.message.includes("fetch")&&!X.message.includes("Failed to fetch")){n.n=36;break}if(A(10,"检测网络连接..."),n.p=28,null==(Z=E())||null===(q=Z.api)||void 0===q||null===(q=q.url)||void 0===q||!q.includes("/api/chat-messages")){n.n=29;break}K=Z.api.url.replace("/api/chat-messages",""),$="".concat(K,"/api/ping"),n.n=31;break;case 29:if(null==Z||null===(J=Z.api)||void 0===J||!J.url){n.n=30;break}$=Z.api.url.replace("/chat/completions","/models"),n.n=31;break;case 30:throw new Error("无法进行连接测试：未找到API配置");case 31:return n.n=32,fetch($,{method:"GET",headers:{Authorization:"Bearer ".concat((null==Z||null===(U=Z.api)||void 0===U?void 0:U.bearerToken)||"")}});case 32:if(n.v.ok){n.n=33;break}throw new Error("服务器连接失败");case 33:throw new Error("API调用失败，请重试");case 34:throw n.p=34,n.v,new Error(H.networkError||"网络连接失败，请检查网络连接");case 35:n.n=39;break;case 36:if(!X.message.includes("401")){n.n=37;break}throw new Error(H.authError||"API认证失败，请检查API密钥");case 37:if(!X.message.includes("429")){n.n=38;break}throw new Error(H.rateLimitError||"API调用频率过高，请稍后再试");case 38:throw new Error("".concat(H.apiError||"API调用失败",": ").concat(X.message));case 39:return n.a(2)}},n,null,[[28,34],[13,18],[11,21,22,23],[0,27]])}))).apply(this,arguments)}function W(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",t=document.getElementById("status");t.textContent=e,t.className="status ".concat(n),t.style.display="block","info"!==n&&"warning"!==n||setTimeout(function(){t.style.display="none"},3e3)}function L(e){var n,t,o=(null===(n=a)||void 0===n||null===(n=n.ui)||void 0===n?void 0:n.messages)||{},r=(null===(t=a)||void 0===t?void 0:t.settings)||{};if(!e||""===e.trim())return!1;var c=r.maxTextLength||5e3;if(e.length>c){var s=o.textTooLong||"文本过长，请选择少于".concat(c,"字符的文本");return W("⚠️ ".concat(s),"warning"),!1}if(0===e.trim().length){var i=o.emptyText||"请选择包含实际内容的文本";return W("⚠️ ".concat(i),"warning"),!1}return!0}function M(e,n){try{var t={timestamp:(new Date).toISOString(),action:e,success:n,userAgent:navigator.userAgent},o=JSON.parse(localStorage.getItem("deepseek-optimizer-logs")||"[]");o.push(t),o.length>100&&o.splice(0,o.length-100),localStorage.setItem("deepseek-optimizer-logs",JSON.stringify(o))}catch(e){console.warn("无法记录使用统计:",e)}}!function(){f.apply(this,arguments)}(),window.switchMode=w,window.processSelectedText=I,window.confirmChanges=function(){return O.apply(this,arguments)},window.cancelChanges=function(){var e;document.getElementById("preview-section").style.display="none",l="",i="",u=null;var n=(null===(e=a)||void 0===e||null===(e=e.ui)||void 0===e?void 0:e.messages)||{};W("❌ ".concat(n.cancelled||"已取消修改"),"info")}}();
//# sourceMappingURL=taskpane.js.map
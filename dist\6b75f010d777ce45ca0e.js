/*! For license information please see 6b75f010d777ce45ca0e.js.LICENSE.txt */
function _createForOfIteratorHelper(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=_unsupportedIterableToArray(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,c=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return c=e.done,e},e:function(e){i=!0,a=e},f:function(){try{c||null==t.return||t.return()}finally{if(i)throw a}}}}function _unsupportedIterableToArray(e,n){if(e){if("string"==typeof e)return _arrayLikeToArray(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(e,n):void 0}}function _arrayLikeToArray(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=Array(n);t<n;t++)o[t]=e[t];return o}function _regenerator(){var e,n,t="function"==typeof Symbol?Symbol:{},o=t.iterator||"@@iterator",r=t.toStringTag||"@@toStringTag";function a(t,o,r,a){var s=o&&o.prototype instanceof i?o:i,l=Object.create(s.prototype);return _regeneratorDefine2(l,"_invoke",function(t,o,r){var a,i,s,l=0,u=r||[],d=!1,f={p:0,n:0,v:e,a:g,f:g.bind(e,4),d:function(n,t){return a=n,i=0,s=e,f.n=t,c}};function g(t,o){for(i=t,s=o,n=0;!d&&l&&!r&&n<u.length;n++){var r,a=u[n],g=f.p,p=a[2];t>3?(r=p===o)&&(s=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=e):a[0]<=g&&((r=t<2&&g<a[1])?(i=0,f.v=o,f.n=a[1]):g<p&&(r=t<3||a[0]>o||o>p)&&(a[4]=t,a[5]=o,f.n=p,i=0))}if(r||t>1)return c;throw d=!0,o}return function(r,u,p){if(l>1)throw TypeError("Generator is already running");for(d&&1===u&&g(u,p),i=u,s=p;(n=i<2?e:s)||!d;){a||(i?i<3?(i>1&&(f.n=-1),g(i,s)):f.n=s:f.v=s);try{if(l=2,a){if(i||(r="next"),n=a[r]){if(!(n=n.call(a,s)))throw TypeError("iterator result is not an object");if(!n.done)return n;s=n.value,i<2&&(i=0)}else 1===i&&(n=a.return)&&n.call(a),i<2&&(s=TypeError("The iterator does not provide a '"+r+"' method"),i=1);a=e}else if((n=(d=f.n<0)?s:t.call(o,f))!==c)break}catch(n){a=e,i=1,s=n}finally{l=1}}return{value:n,done:d}}}(t,r,a),!0),l}var c={};function i(){}function s(){}function l(){}n=Object.getPrototypeOf;var u=[][o]?n(n([][o]())):(_regeneratorDefine2(n={},o,function(){return this}),n),d=l.prototype=i.prototype=Object.create(u);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,_regeneratorDefine2(e,r,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=l,_regeneratorDefine2(d,"constructor",l),_regeneratorDefine2(l,"constructor",s),s.displayName="GeneratorFunction",_regeneratorDefine2(l,r,"GeneratorFunction"),_regeneratorDefine2(d),_regeneratorDefine2(d,r,"Generator"),_regeneratorDefine2(d,o,function(){return this}),_regeneratorDefine2(d,"toString",function(){return"[object Generator]"}),(_regenerator=function(){return{w:a,m:f}})()}function _regeneratorDefine2(e,n,t,o){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}_regeneratorDefine2=function(e,n,t,o){function a(n,t){_regeneratorDefine2(e,n,function(e){return this._invoke(n,t,e)})}n?r?r(e,n,{value:t,enumerable:!o,configurable:!o,writable:!o}):e[n]=t:(a("next",0),a("throw",1),a("return",2))},_regeneratorDefine2(e,n,t,o)}function asyncGeneratorStep(e,n,t,o,r,a,c){try{var i=e[a](c),s=i.value}catch(e){return void t(e)}i.done?n(s):Promise.resolve(s).then(o,r)}function _asyncToGenerator(e){return function(){var n=this,t=arguments;return new Promise(function(o,r){var a=e.apply(n,t);function c(e){asyncGeneratorStep(a,o,r,c,i,"next",e)}function i(e){asyncGeneratorStep(a,o,r,c,i,"throw",e)}c(void 0)})}}var config=null,currentMode="optimize",originalText="",generatedText="",currentSelection=null;function waitForOffice(){return new Promise(function(e){if("undefined"!=typeof Office&&Office.onReady)e();else{var n=function(){"undefined"!=typeof Office&&Office.onReady?e():setTimeout(n,100)};setTimeout(n,100)}})}function initialize(){return _initialize.apply(this,arguments)}function _initialize(){return(_initialize=_asyncToGenerator(_regenerator().m(function e(){var n;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:return console.log("Starting initialization..."),e.p=1,e.n=2,Promise.race([waitForOffice(),new Promise(function(e,n){return setTimeout(function(){return n(new Error("Office.js timeout"))},5e3)})]);case 2:console.log("Office.js loaded, calling Office.onReady..."),Office.onReady(function(e){console.log("Office.onReady called, info:",e),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",initializeApp):initializeApp()}).catch(function(e){console.error("Office.onReady error:",e),initializeAppFallback()}),e.n=4;break;case 3:e.p=3,n=e.v,console.warn("Office.js not available or timeout:",n),initializeAppFallback();case 4:return e.a(2)}},e,null,[[1,3]])}))).apply(this,arguments)}function initializeAppFallback(){console.log("Fallback: initializing app without Office context..."),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",initializeApp):initializeApp()}function initializeApp(){return _initializeApp.apply(this,arguments)}function _initializeApp(){return(_initializeApp=_asyncToGenerator(_regenerator().m(function e(){var n,t;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:return console.log("Initializing app..."),e.p=1,console.log("Office object:","undefined"!=typeof Office?Office:"undefined"),console.log("Word object:","undefined"!=typeof Word?Word:"undefined"),e.n=2,loadConfig();case 2:initializeUI(),bindEvents(),e.n=4;break;case 3:e.p=3,t=e.v,console.error("Failed to initialize app:",t);try{(n=document.getElementById("status"))&&(n.textContent="❌ 应用初始化失败: ".concat(t.message),n.className="status error",n.style.display="block"),["mode-tabs","custom-input-section","instructions","process-button"].forEach(function(e){var n=document.getElementById(e);n&&(n.style.display="none")})}catch(e){console.error("Failed to show status:",e)}case 4:return e.a(2)}},e,null,[[1,3]])}))).apply(this,arguments)}function loadConfig(){return _loadConfig.apply(this,arguments)}function _loadConfig(){return(_loadConfig=_asyncToGenerator(_regenerator().m(function e(){var n,t,o,r,a,c,i,s;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:t=null,o=0,r=["./conf.json","conf.json","../conf.json","/conf.json"];case 1:if(!(o<r.length)){e.n=10;break}return a=r[o],e.p=2,e.n=3,fetch(a);case 3:if(!(c=e.v).ok){e.n=6;break}return e.n=4,c.text();case 4:if(i=e.v,(config=JSON.parse(i)).sections&&Array.isArray(config.sections)&&0!==config.sections.length){e.n=5;break}throw new Error("Invalid config: sections is empty or not an array");case 5:return console.log("✅ Config loaded: ".concat(config.sections.length," sections from ").concat(a)),e.a(2);case 6:t=new Error("HTTP ".concat(c.status," for ").concat(a));case 7:e.n=9;break;case 8:e.p=8,s=e.v,t=s;case 9:o++,e.n=1;break;case 10:throw console.error("❌ Failed to load config from all paths"),new Error("配置文件加载失败: ".concat((null===(n=t)||void 0===n?void 0:n.message)||"所有路径都无法访问"));case 11:return e.a(2)}},e,null,[[2,8]])}))).apply(this,arguments)}function initializeUI(){var e;if(config)if(config.sections&&Array.isArray(config.sections)){var n=document.getElementById("header");n&&config.app?n.innerHTML="\n      <h1>".concat(config.app.title,"</h1>\n      <p>").concat(config.app.subtitle,"</p>\n    "):console.error("❌ Header element not found or app config missing");var t=document.getElementById("mode-tabs");if(t){console.log("🏷️ Generating tabs for sections:",config.sections.map(function(e){return e.name})),t.innerHTML="",config.sections.forEach(function(e,n){console.log("🏷️ Creating tab ".concat(n+1,": ").concat(e.name," (").concat(e.id,")"));var o=document.createElement("button");o.id="".concat(e.id,"-tab"),o.className="mode-tab ".concat(0===n?"active":""),o.textContent=e.name,o.onclick=function(){switchMode(e.id)},t.appendChild(o)}),console.log("✅ Created ".concat(config.sections.length," tabs")),config.sections.length>0&&(currentMode=config.sections[0].id,console.log("🎯 Default mode set to: ".concat(currentMode)));var o=document.getElementById("instructions");if(o&&null!==(e=config.ui)&&void 0!==e&&e.instructions){var r=config.ui.instructions;o.innerHTML="\n      <h3>".concat(r.title,"</h3>\n      <ol>\n        ").concat(r.steps.map(function(e){return"<li>".concat(e,"</li>")}).join(""),"\n      </ol>\n    ")}else console.warn("⚠️ Instructions element or config not found"),o&&(o.style.display="none");updateButtonText()}else console.error("❌ mode-tabs element not found")}else console.error("❌ Config sections is not an array:",config.sections);else console.error("❌ Config is null or undefined!")}function bindEvents(){var e=document.getElementById("process-button");e?e.onclick=processSelectedText:console.error("Process button not found")}function switchMode(e){currentMode=e,document.querySelectorAll(".mode-tab").forEach(function(e){e.classList.remove("active")});var n=document.getElementById("".concat(e,"-tab"));n&&(n.classList.add("active"),console.log("Added active class to tab: ".concat(n.id))),updateButtonText();var t=getCurrentSection(),o=document.getElementById("custom-input-section");if(t&&t.showCustomInput){o.style.display="block";var r=document.getElementById("custom-input");r&&t.customInputPlaceholder&&(r.placeholder=t.customInputPlaceholder)}else o.style.display="none"}function updateButtonText(){var e=document.getElementById("button-text"),n=getCurrentSection();e&&n&&(e.textContent=n.buttonText,console.log("Updated button text to: ".concat(n.buttonText)))}function getCurrentSection(){return config&&config.sections?config.sections.find(function(e){return e.id===currentMode}):null}function processSelectedText(){return _processSelectedText.apply(this,arguments)}function _processSelectedText(){return _processSelectedText=_asyncToGenerator(_regenerator().m(function e(){var n,t,o,r,a,c,i,s,l,u,d,f;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:if(console.log("processSelectedText called, current mode:",currentMode),n=document.getElementById("process-button"),t=document.getElementById("progress-section"),o=document.getElementById("stream-output"),r=document.getElementById("preview-section"),a=document.getElementById("hint-section"),c=document.getElementById("status"),i=!1,s=Date.now(),e.p=1,r.style.display="none",a.style.display="none",c.style.display="none",n.disabled=!0,t.style.display="block",o.style.display="block",updateProgress(0,"准备中..."),clearStreamOutput(),"undefined"==typeof Word||!Word.run){e.n=3;break}return e.n=2,Word.run(function(){var e=_asyncToGenerator(_regenerator().m(function e(n){var r,a,c,l,u,d,f,g,p;return _regenerator().w(function(e){for(;;)switch(e.n){case 0:return(r=n.document.getSelection()).load("text"),e.n=1,n.sync();case 1:if(validateText(r.text)){e.n=2;break}throw c=(null===(a=config)||void 0===a||null===(a=a.ui)||void 0===a?void 0:a.messages)||{},new Error(c.noSelection||"请先选择有效的文本内容");case 2:return originalText=r.text,currentSelection=r,updateProgress(20,"开始处理文本..."),e.n=3,callModelAPI(r.text,currentMode);case 3:(l=e.v)&&l!==r.text?(updateProgress(100,"生成完成！"),generatedText=l,t.style.display="none",o.style.display="none",showPreview(generatedText),d=Date.now()-s,f=(null===(u=config)||void 0===u||null===(u=u.ui)||void 0===u?void 0:u.messages)||{},g=getCurrentSection(),p=(null==g?void 0:g.name)||"处理",showStatus("✅ ".concat(p).concat(f.success||"完成！","(耗时: ").concat(Math.round(d/1e3),"秒)"),"info"),i=!0):(updateProgress(100,"无需处理"),showStatus("⚠️ 文本无需处理或AI返回相同内容","warning"),i=!0);case 4:return e.a(2)}},e)}));return function(n){return e.apply(this,arguments)}}());case 2:e.n=4;break;case 3:console.log("Running in browser environment, simulating processing..."),originalText="这是一个测试文本，用于演示功能。",updateProgress(20,"开始处理文本..."),setTimeout(_asyncToGenerator(_regenerator().m(function e(){var n,r,a,c,l,u,d;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,callModelAPI("这是一个测试文本，用于演示功能。",currentMode);case 1:r=e.v,updateProgress(100,"生成完成！"),generatedText=r,t.style.display="none",o.style.display="none",showPreview(generatedText),a=Date.now()-s,c=(null===(n=config)||void 0===n||null===(n=n.ui)||void 0===n?void 0:n.messages)||{},l=getCurrentSection(),u=(null==l?void 0:l.name)||"处理",showStatus("✅ ".concat(u).concat(c.success||"完成！","(耗时: ").concat(Math.round(a/1e3),"秒)"),"info"),i=!0,e.n=3;break;case 2:e.p=2,d=e.v,console.error("API调用失败:",d),showStatus("❌ API调用失败: ".concat(d.message),"error"),updateProgress(0,"处理失败");case 3:return e.a(2)}},e,null,[[0,2]])})),1e3);case 4:e.n=6;break;case 5:e.p=5,f=e.v,console.error("处理文本时出错:",f),u=f.message,d=(null===(l=config)||void 0===l||null===(l=l.ui)||void 0===l?void 0:l.messages)||{},f.message.includes("网络")?u=d.networkError||f.message:f.message.includes("API")?u=d.apiError||f.message:f.message.includes("选择")?u=d.noSelection||f.message:f.message.includes("配置")&&(u=f.message),showStatus("❌ ".concat(u),"error"),updateProgress(0,"处理失败");case 6:return e.p=6,n.disabled=!1,logUsage("".concat(currentMode,"_text"),i),e.f(6);case 7:return e.a(2)}},e,null,[[1,5,6,7]])})),_processSelectedText.apply(this,arguments)}function showPreview(e){var n=document.getElementById("preview-section");document.getElementById("preview-content").textContent=e,n.style.display="block"}function confirmChanges(){return _confirmChanges.apply(this,arguments)}function _confirmChanges(){return _confirmChanges=_asyncToGenerator(_regenerator().m(function e(){var n,t,o;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:if(generatedText&&currentSelection){e.n=1;break}return showStatus("❌ 没有可确认的更改","error"),e.a(2);case 1:if(e.p=1,"undefined"==typeof Word||!Word.run){e.n=3;break}return e.n=2,Word.run(function(){var e=_asyncToGenerator(_regenerator().m(function e(n){var t,o,r,a;return _regenerator().w(function(e){for(;;)switch(e.n){case 0:return(o=n.document.getSelection()).load("text"),e.n=1,n.sync();case 1:if(!o.text||o.text.trim()!==originalText.trim()){e.n=3;break}return o.insertText(generatedText,Word.InsertLocation.replace),e.n=2,n.sync();case 2:e.n=7;break;case 3:return(r=n.document.body.search(originalText.substring(0,50),{matchCase:!1,matchWholeWord:!1})).load("items"),e.n=4,n.sync();case 4:if(!(r.items.length>0)){e.n=6;break}return r.items[0].insertText(generatedText,Word.InsertLocation.replace),e.n=5,n.sync();case 5:e.n=7;break;case 6:throw new Error("无法找到要替换的原始文本");case 7:document.getElementById("preview-section").style.display="none",showHint(),a=(null===(t=config)||void 0===t||null===(t=t.ui)||void 0===t?void 0:t.messages)||{},showStatus("✅ ".concat(a.applied||"修改已应用到文档"),"info");case 8:return e.a(2)}},e)}));return function(n){return e.apply(this,arguments)}}());case 2:e.n=4;break;case 3:document.getElementById("preview-section").style.display="none",showHint(),t=(null===(n=config)||void 0===n||null===(n=n.ui)||void 0===n?void 0:n.messages)||{},showStatus("✅ ".concat(t.applied||"修改已应用到文档","（模拟）"),"info");case 4:e.n=6;break;case 5:e.p=5,o=e.v,console.error("确认更改时出错:",o),showStatus("❌ 应用更改失败: ".concat(o.message),"error");case 6:return e.a(2)}},e,null,[[1,5]])})),_confirmChanges.apply(this,arguments)}function cancelChanges(){var e;document.getElementById("preview-section").style.display="none",generatedText="",originalText="",currentSelection=null;var n=(null===(e=config)||void 0===e||null===(e=e.ui)||void 0===e?void 0:e.messages)||{};showStatus("❌ ".concat(n.cancelled||"已取消修改"),"info")}function showHint(){var e=document.getElementById("hint-section"),n=document.getElementById("hint-text");if(n){var t,o=(null===(t=config)||void 0===t||null===(t=t.ui)||void 0===t?void 0:t.messages)||{};n.textContent=o.undoHint||"修改已应用到文档，在文档中按 Ctrl+Z 可撤销此次修改"}e.style.display="block"}function updateProgress(e,n){var t=document.getElementById("progress-fill"),o=document.getElementById("progress-text");t.style.width="".concat(e,"%"),o.textContent=n}function clearStreamOutput(){document.getElementById("stream-content").textContent=""}function appendStreamOutput(e){document.getElementById("stream-content").textContent+=e;var n=document.getElementById("stream-output");n.scrollTop=n.scrollHeight}function callModelAPI(e,n){return _callModelAPI.apply(this,arguments)}function _callModelAPI(){return(_callModelAPI=_asyncToGenerator(_regenerator().m(function e(n,t){var o,r,a,c,i,s,l,u,d,f,g,p,m,y,h,v,w,b,k,_,T,I,S,x,E,A,P,C,O,B,M,D,z,j,G,L,W,F,R,H,N,U,q;return _regenerator().w(function(e){for(;;)switch(e.p=e.n){case 0:if(e.p=0,updateProgress(30,"正在生成内容..."),o=getCurrentSection()){e.n=1;break}throw new Error("未找到模式配置: ".concat(t));case 1:if(r=o.api,a=o.systemPrompt,console.log("🚀 API Request - Mode: ".concat(t)),console.log("🔗 API URL: ".concat(r.url)),console.log("🤖 Model: ".concat(r.model)),console.log("🌡️ Temperature: ".concat(r.temperature)),console.log("📝 Max Tokens: ".concat(r.maxTokens)),console.log("📄 Input Text Length: ".concat(n.length," chars")),!o.showCustomInput){e.n=3;break}if(i=document.getElementById("custom-input"),s=i?i.value.trim():""){e.n=2;break}throw u=(null===(l=config)||void 0===l||null===(l=l.ui)||void 0===l?void 0:l.messages)||{},new Error(u.customInputRequired||"请输入自定义处理要求");case 2:c="".concat(s,"\n\n要处理的文本：\n").concat(n),console.log("🎯 Custom Requirement: ".concat(s)),e.n=4;break;case 3:c=n;case 4:return console.log("💬 System Prompt: ".concat(a.substring(0,100),"...")),console.log("👤 User Message Length: ".concat(c.length," chars")),d=r.url.includes("/api/chat-messages")||"dify"===r.apiType?{response_mode:"streaming",conversation_id:"",query:c,inputs:{}}:{model:r.model,messages:[{role:"system",content:a},{role:"user",content:c}],temperature:r.temperature,max_tokens:r.maxTokens,stream:"streaming"===r.responseMode},console.log("📤 Sending API request..."),f=Date.now(),g={Authorization:"Bearer ".concat(r.bearerToken),"Content-Type":"application/json"},e.n=5,fetch(r.url,{method:"POST",headers:g,body:JSON.stringify(d)});case 5:if(p=e.v,m=Date.now()-f,console.log("📡 API Response - Status: ".concat(p.status,", Time: ").concat(m,"ms")),p.ok){e.n=7;break}return e.n=6,p.text();case 6:throw y=e.v,console.error("❌ API Error Response: ".concat(y)),new Error("API请求失败 (".concat(p.status,"): ").concat(y));case 7:updateProgress(40,"正在接收数据..."),h=p.body.getReader(),v=new TextDecoder,w="",b="",k=0,_=0,console.log("📥 Starting stream processing...");case 8:return e.n=9,h.read();case 9:if(T=e.v,I=T.done,S=T.value,!I){e.n=10;break}return e.a(3,24);case 10:k++,_+=S.length,b+=v.decode(S,{stream:!0}),x=b.split("\n"),b=x.pop(),E=_createForOfIteratorHelper(x),e.p=11,E.s();case 12:if((A=E.n()).done){e.n=20;break}if(!(P=A.value).startsWith("data: ")){e.n=19;break}if("[DONE]"!==(C=P.slice(6))){e.n=13;break}return console.log("🏁 Stream completed - Chunks: ".concat(k,", Bytes: ").concat(_,", Result length: ").concat(w.length)),e.a(3,20);case 13:if(e.p=13,O=JSON.parse(C),B="","message"!==O.event||!O.answer){e.n=14;break}B=O.answer,e.n=17;break;case 14:if("message_replace"!==O.event||!O.answer){e.n=15;break}return w=O.answer,clearStreamOutput(),appendStreamOutput(w),updateProgress(Math.min(50+w.length/n.length*30,80),"正在生成内容..."),e.a(2);case 15:if("message_end"!==O.event){e.n=16;break}return console.log("✅ Dify message completed"),e.a(2);case 16:O.choices&&O.choices[0]&&O.choices[0].delta&&O.choices[0].delta.content&&(B=O.choices[0].delta.content);case 17:B&&(w+=B,appendStreamOutput(B),updateProgress(Math.min(50+w.length/n.length*30,80),"正在生成内容...")),e.n=19;break;case 18:e.p=18,N=e.v,console.warn("❌ Stream parsing error:",N.message,"Data:",C);case 19:e.n=12;break;case 20:e.n=22;break;case 21:e.p=21,U=e.v,E.e(U);case 22:return e.p=22,E.f(),e.f(22);case 23:e.n=8;break;case 24:if(!w.trim()){e.n=25;break}return((M=w.trim()).startsWith('"')&&M.endsWith('"')||M.startsWith("'")&&M.endsWith("'"))&&(M=M.slice(1,-1)),console.log("✅ API Success - Output length: ".concat(M.length," chars")),console.log("📊 Compression ratio: ".concat(((n.length-M.length)/n.length*100).toFixed(1),"%")),e.a(2,M);case 25:throw console.error("❌ API returned empty content"),new Error("API返回空内容");case 26:e.n=39;break;case 27:if(e.p=27,q=e.v,console.error("❌ API Call Failed:",q.message),console.error("🔍 Error Details:",{name:q.name,message:q.message,stack:null===(D=q.stack)||void 0===D?void 0:D.split("\n")[0]}),j=(null===(z=config)||void 0===z||null===(z=z.ui)||void 0===z?void 0:z.messages)||{},!q.message.includes("fetch")&&!q.message.includes("Failed to fetch")){e.n=36;break}if(updateProgress(10,"检测网络连接..."),e.p=28,null==(F=getCurrentSection())||null===(G=F.api)||void 0===G||null===(G=G.url)||void 0===G||!G.includes("/api/chat-messages")){e.n=29;break}H=F.api.url.replace("/api/chat-messages",""),R="".concat(H,"/api/ping"),e.n=31;break;case 29:if(null==F||null===(L=F.api)||void 0===L||!L.url){e.n=30;break}R=F.api.url.replace("/chat/completions","/models"),e.n=31;break;case 30:throw new Error("无法进行连接测试：未找到API配置");case 31:return e.n=32,fetch(R,{method:"GET",headers:{Authorization:"Bearer ".concat((null==F||null===(W=F.api)||void 0===W?void 0:W.bearerToken)||"")}});case 32:if(e.v.ok){e.n=33;break}throw new Error("服务器连接失败");case 33:throw new Error("API调用失败，请重试");case 34:throw e.p=34,e.v,new Error(j.networkError||"网络连接失败，请检查网络连接");case 35:e.n=39;break;case 36:if(!q.message.includes("401")){e.n=37;break}throw new Error(j.authError||"API认证失败，请检查API密钥");case 37:if(!q.message.includes("429")){e.n=38;break}throw new Error(j.rateLimitError||"API调用频率过高，请稍后再试");case 38:throw new Error("".concat(j.apiError||"API调用失败",": ").concat(q.message));case 39:return e.a(2)}},e,null,[[28,34],[13,18],[11,21,22,23],[0,27]])}))).apply(this,arguments)}function showStatus(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",t=document.getElementById("status");t.textContent=e,t.className="status ".concat(n),t.style.display="block","info"!==n&&"warning"!==n||setTimeout(function(){t.style.display="none"},3e3)}function validateText(e){var n,t,o=(null===(n=config)||void 0===n||null===(n=n.ui)||void 0===n?void 0:n.messages)||{},r=(null===(t=config)||void 0===t?void 0:t.settings)||{};if(!e||""===e.trim())return!1;var a=r.maxTextLength||5e3;if(e.length>a){var c=o.textTooLong||"文本过长，请选择少于".concat(a,"字符的文本");return showStatus("⚠️ ".concat(c),"warning"),!1}if(0===e.trim().length){var i=o.emptyText||"请选择包含实际内容的文本";return showStatus("⚠️ ".concat(i),"warning"),!1}return!0}function logUsage(e,n){try{var t={timestamp:(new Date).toISOString(),action:e,success:n,userAgent:navigator.userAgent},o=JSON.parse(localStorage.getItem("deepseek-optimizer-logs")||"[]");o.push(t),o.length>100&&o.splice(0,o.length-100),localStorage.setItem("deepseek-optimizer-logs",JSON.stringify(o))}catch(e){console.warn("无法记录使用统计:",e)}}initialize(),window.switchMode=switchMode,window.processSelectedText=processSelectedText,window.confirmChanges=confirmChanges,window.cancelChanges=cancelChanges;
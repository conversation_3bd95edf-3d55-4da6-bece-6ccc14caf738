{"name": "word-deepseek-optimizer", "version": "1.0.0", "description": "Office Word加载项，使用DeepSeek AI优化选中文本", "main": "src/taskpane/taskpane.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "dev-server": "webpack serve --mode development --port 3002", "start": "office-addin-debugging start manifest.xml", "stop": "office-addin-debugging stop manifest.xml", "validate": "office-addin-manifest validate manifest.xml", "sideload": "office-addin-dev-settings sideload manifest.xml"}, "keywords": ["Office Add-in", "Word", "DeepSeek", "AI", "Text Optimization"], "author": "AI Office Team", "license": "MIT", "dependencies": {"core-js": "^3.31.0", "regenerator-runtime": "^0.13.11"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.43.0", "file-loader": "^6.2.0", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.3", "office-addin-debugging": "^5.0.3", "office-addin-dev-certs": "^1.13.5", "office-addin-lint": "^2.2.3", "office-addin-manifest": "^1.12.3", "office-addin-prettier-config": "^1.2.0", "source-map-loader": "^4.0.1", "style-loader": "^3.3.3", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "browserslist": ["ie 11"]}
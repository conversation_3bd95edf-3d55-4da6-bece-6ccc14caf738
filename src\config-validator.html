<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文本优化工具 - 配置验证器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 8px;
        }

        .content {
            padding: 25px;
        }

        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .config-item {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #ddd;
        }

        .config-item.success { border-left-color: #4caf50; background: #f1f8e9; }
        .config-item.error { border-left-color: #f44336; background: #ffebee; }
        .config-item.warning { border-left-color: #ff9800; background: #fff3e0; }
        .config-item.testing { border-left-color: #2196f3; background: #e3f2fd; }

        .config-title {
            font-weight: bold;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-icon.success { background: #4caf50; }
        .status-icon.error { background: #f44336; }
        .status-icon.warning { background: #ff9800; }
        .status-icon.testing { background: #2196f3; }

        .config-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 8px 5px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            max-height: 250px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .summary {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .summary h3 {
            margin-bottom: 10px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 6px;
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
        }

        .stat-label {
            font-size: 0.85em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container { margin: 10px; }
            .header { padding: 20px; }
            .header h1 { font-size: 1.8em; }
            .content { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 配置验证器</h1>
            <p>验证 AI 文本优化工具的 API 配置</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📋 配置概览</h2>
                <div id="config-overview">
                    <div class="config-item testing">
                        <div class="config-title">
                            <span class="status-icon testing"></span>
                            正在加载配置...
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🧪 API 连接测试</h2>
                <div>
                    <button class="test-button" onclick="testAllConfigs()">测试所有配置</button>
                    <button class="test-button" onclick="testSpecificConfig('optimize')">测试优化</button>
                    <button class="test-button" onclick="testSpecificConfig('expand')">测试扩写</button>
                    <button class="test-button" onclick="testSpecificConfig('custom')">测试自定义</button>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                
                <div id="test-results"></div>
            </div>

            <div class="section">
                <h2>📊 测试日志</h2>
                <div class="log-container" id="log-container">
                    <div>等待开始测试...</div>
                </div>
            </div>

            <div id="summary-section" style="display: none;">
                <div class="summary">
                    <h3>📈 测试总结</h3>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="total-tests">0</div>
                            <div class="stat-label">总测试数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="passed-tests">0</div>
                            <div class="stat-label">通过测试</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="failed-tests">0</div>
                            <div class="stat-label">失败测试</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="success-rate">0%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let config = null;
        let testResults = [];
        let currentTestIndex = 0;

        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await loadConfiguration();
            displayConfigOverview();
        });

        // 加载配置文件
        async function loadConfiguration() {
            try {
                const response = await fetch('./conf.json');
                if (response.ok) {
                    config = await response.json();
                    log('✅ 配置文件加载成功');
                    return;
                }
            } catch (error) {
                log(`❌ 配置加载失败: ${error.message}`);
            }
            
            log('❌ 无法加载配置文件');
            throw new Error('配置文件加载失败');
        }

        // 显示配置概览
        function displayConfigOverview() {
            const overviewContainer = document.getElementById('config-overview');
            
            if (!config) {
                overviewContainer.innerHTML = `
                    <div class="config-item error">
                        <div class="config-title">
                            <span class="status-icon error"></span>
                            配置加载失败
                        </div>
                        <div class="config-details">无法加载 conf.json 文件</div>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="config-item success">
                    <div class="config-title">
                        <span class="status-icon success"></span>
                        应用信息
                    </div>
                    <div class="config-details">
                        标题: ${config.app?.title || '未设置'}<br>
                        副标题: ${config.app?.subtitle || '未设置'}
                    </div>
                </div>
            `;

            if (config.sections && Array.isArray(config.sections)) {
                config.sections.forEach(section => {
                    const apiType = detectApiType(section.api?.url, section.api?.apiType);
                    html += `
                        <div class="config-item ${section.api ? 'success' : 'warning'}">
                            <div class="config-title">
                                <span class="status-icon ${section.api ? 'success' : 'warning'}"></span>
                                ${section.name} (${section.id})
                            </div>
                            <div class="config-details">
                                API类型: ${apiType}<br>
                                URL: ${section.api?.url || '未设置'}<br>
                                模型: ${section.api?.model || '未设置'}<br>
                                响应模式: ${section.api?.responseMode || '未设置'}
                            </div>
                        </div>
                    `;
                });
            }

            overviewContainer.innerHTML = html;
        }

        // 检测API类型
        function detectApiType(url, apiType) {
            if (apiType === 'dify') return 'Dify API';
            if (!url) return '未知';
            if (url.includes('/api/chat-messages')) return 'Dify API';
            if (url.includes('/chat/completions')) return 'OpenAI API';
            return '未知类型';
        }

        // 测试所有配置
        async function testAllConfigs() {
            if (!config || !config.sections) {
                log('❌ 没有可测试的配置');
                return;
            }

            testResults = [];
            currentTestIndex = 0;

            const testButtons = document.querySelectorAll('.test-button');
            testButtons.forEach(btn => btn.disabled = true);

            document.getElementById('summary-section').style.display = 'none';
            document.getElementById('test-results').innerHTML = '';

            log('🚀 开始测试所有配置...');

            for (const section of config.sections) {
                await testSpecificConfig(section.id, false);
            }

            testButtons.forEach(btn => btn.disabled = false);
            updateProgress(100);
            showSummary();
            log('🏁 所有测试完成');
        }

        // 测试特定配置
        async function testSpecificConfig(sectionId, standalone = true) {
            const section = config.sections.find(s => s.id === sectionId);
            if (!section) {
                log(`❌ 未找到配置: ${sectionId}`);
                return;
            }

            if (standalone) {
                testResults = [];
                currentTestIndex = 0;
                document.getElementById('test-results').innerHTML = '';
                document.getElementById('summary-section').style.display = 'none';
            }

            const testResult = {
                sectionId,
                sectionName: section.name,
                tests: []
            };

            log(`🧪 测试配置: ${section.name} (${sectionId})`);

            // 创建测试结果容器
            const resultContainer = document.createElement('div');
            resultContainer.className = 'config-item testing';
            resultContainer.id = `test-${sectionId}`;
            resultContainer.innerHTML = `
                <div class="config-title">
                    <span class="status-icon testing"></span>
                    ${section.name} - 测试中...
                </div>
                <div class="config-details" id="details-${sectionId}">正在进行连接测试...</div>
            `;
            document.getElementById('test-results').appendChild(resultContainer);

            // 测试1: 配置完整性
            const configTest = testConfigCompleteness(section);
            testResult.tests.push(configTest);
            updateTestDetails(sectionId, '配置完整性: ' + (configTest.passed ? '✅ 通过' : '❌ 失败'));

            // 测试2: API连接性
            if (configTest.passed) {
                const connectionTest = await testApiConnection(section);
                testResult.tests.push(connectionTest);
                updateTestDetails(sectionId, 'API连接: ' + (connectionTest.passed ? '✅ 通过' : '❌ 失败'));

                // 测试3: API响应格式
                if (connectionTest.passed) {
                    const responseTest = await testApiResponse(section);
                    testResult.tests.push(responseTest);
                    updateTestDetails(sectionId, '响应格式: ' + (responseTest.passed ? '✅ 通过' : '❌ 失败'));
                }
            }

            // 更新测试结果显示
            const allPassed = testResult.tests.every(test => test.passed);
            const container = document.getElementById(`test-${sectionId}`);
            container.className = `config-item ${allPassed ? 'success' : 'error'}`;
            container.querySelector('.status-icon').className = `status-icon ${allPassed ? 'success' : 'error'}`;
            container.querySelector('.config-title').innerHTML = `
                <span class="status-icon ${allPassed ? 'success' : 'error'}"></span>
                ${section.name} - ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}
            `;

            testResults.push(testResult);
            currentTestIndex++;

            if (!standalone) {
                updateProgress((currentTestIndex / config.sections.length) * 100);
            }

            if (standalone) {
                showSummary();
            }
        }

        // 测试配置完整性
        function testConfigCompleteness(section) {
            const requiredFields = ['url', 'bearerToken', 'model'];
            const missingFields = [];

            for (const field of requiredFields) {
                if (!section.api || !section.api[field]) {
                    missingFields.push(field);
                }
            }

            return {
                name: '配置完整性',
                passed: missingFields.length === 0,
                message: missingFields.length === 0 ?
                    '所有必需字段都已配置' :
                    `缺少字段: ${missingFields.join(', ')}`
            };
        }

        // 测试API连接
        async function testApiConnection(section) {
            try {
                let requestBody;
                if (section.api.url.includes('/api/chat-messages') || section.api.apiType === 'dify') {
                    // 使用真实的Dify API格式
                    requestBody = {
                        response_mode: "streaming",
                        conversation_id: "",
                        query: "测试连接",
                        inputs: {}
                    };
                } else {
                    requestBody = {
                        model: section.api.model,
                        messages: [{ role: 'user', content: '测试连接' }],
                        max_tokens: 10
                    };
                }

                const response = await fetch(section.api.url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${section.api.bearerToken}`
                    },
                    body: JSON.stringify(requestBody)
                });

                return {
                    name: 'API连接',
                    passed: response.ok,
                    message: response.ok ?
                        `连接成功 (${response.status})` :
                        `连接失败 (${response.status}): ${response.statusText}`
                };
            } catch (error) {
                return {
                    name: 'API连接',
                    passed: false,
                    message: `连接错误: ${error.message}`
                };
            }
        }

        // 测试API响应格式
        async function testApiResponse(section) {
            try {
                let requestBody;
                if (section.api.url.includes('/api/chat-messages') || section.api.apiType === 'dify') {
                    // 使用真实的Dify API格式
                    requestBody = {
                        response_mode: "streaming",
                        conversation_id: "",
                        query: "你好",
                        inputs: {}
                    };
                } else {
                    requestBody = {
                        model: section.api.model,
                        messages: [{ role: 'user', content: '你好' }],
                        max_tokens: 10
                    };
                }

                const response = await fetch(section.api.url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${section.api.bearerToken}`
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    return {
                        name: '响应格式',
                        passed: false,
                        message: `HTTP错误: ${response.status}`
                    };
                }

                // 对于流式响应，我们只检查是否能成功建立连接
                return {
                    name: '响应格式',
                    passed: true,
                    message: '流式响应连接成功'
                };
            } catch (error) {
                return {
                    name: '响应格式',
                    passed: false,
                    message: `测试错误: ${error.message}`
                };
            }
        }

        // 更新测试详情
        function updateTestDetails(sectionId, message) {
            const detailsElement = document.getElementById(`details-${sectionId}`);
            if (detailsElement) {
                detailsElement.innerHTML += '<br>' + message;
            }
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressFill = document.getElementById('progress-fill');
            progressFill.style.width = percentage + '%';
        }

        // 显示测试总结
        function showSummary() {
            const totalTests = testResults.reduce((sum, result) => sum + result.tests.length, 0);
            const passedTests = testResults.reduce((sum, result) =>
                sum + result.tests.filter(test => test.passed).length, 0);
            const failedTests = totalTests - passedTests;
            const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

            document.getElementById('total-tests').textContent = totalTests;
            document.getElementById('passed-tests').textContent = passedTests;
            document.getElementById('failed-tests').textContent = failedTests;
            document.getElementById('success-rate').textContent = successRate + '%';

            document.getElementById('summary-section').style.display = 'block';
        }

        // 记录日志
        function log(message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>
</html>

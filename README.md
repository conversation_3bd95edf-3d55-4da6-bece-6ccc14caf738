# DeepSeek文本优化器 - Office Word加载项

这是一个Office Word加载项，使用DeepSeek AI来优化选中的文本内容。

## 功能特性

- 🤖 **AI驱动优化**: 使用DeepSeek R1模型优化文本
- 📝 **无缝集成**: 直接在Word中使用，无需切换应用
- 🎯 **智能优化**: 保持原意的同时改善表达方式、语法和结构
- ⚡ **快速响应**: 实时优化，即选即用
- 🔒 **安全可靠**: 使用HTTPS加密传输

## 安装和使用

### 前置要求

- Node.js (版本 14 或更高)
- Microsoft Word (Office 365 或 Word 2016+)
- Windows 10/11 或 macOS

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd word-deepseek-optimizer
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **生成开发证书**
   ```bash
   npx office-addin-dev-certs install
   ```

4. **启动开发服务器**
   ```bash
   npm run dev-server
   ```

5. **在Word中加载加载项**
   ```bash
   npm start
   ```

### 使用方法

1. 在Word文档中选择要优化的文本
2. 点击Ribbon菜单中的"使用DeepSeek优化"按钮
3. 等待AI处理完成，优化后的文本将自动替换原文

## 开发说明

### 项目结构

```
word-deepseek-optimizer/
├── src/
│   ├── taskpane/          # 任务窗格相关文件
│   │   ├── taskpane.html  # 任务窗格HTML
│   │   └── taskpane.js    # 任务窗格JavaScript
│   └── commands/          # 命令相关文件
│       ├── commands.html  # 命令HTML
│       └── commands.js    # 命令JavaScript
├── assets/                # 图标和资源文件
├── manifest.xml           # 加载项清单文件
├── package.json           # 项目配置
└── webpack.config.js      # Webpack配置
```

### 可用脚本

- `npm run dev-server` - 启动开发服务器
- `npm start` - 在Word中启动加载项
- `npm run build` - 构建生产版本
- `npm run validate` - 验证清单文件

### API配置

本工具支持两种API格式：

- **OpenAI格式API**: optimize、custom功能
- **Dify API**: expand功能
- **自动适配**: 根据URL格式自动选择API类型
- **认证**: Bearer Token / JWT Token

### 配置管理

所有配置集中在 `src/conf.json` 文件中，部署时只需修改此文件：

```json
{
  "sections": [
    {
      "id": "optimize",
      "api": {
        "url": "https://your-api-server/v1/chat/completions",
        "bearerToken": "your-api-key",
        "model": "your-model"
      }
    },
    {
      "id": "expand",
      "api": {
        "url": "http://your-dify-server/api/chat-messages",
        "bearerToken": "your-jwt-token",
        "model": "your-dify-model"
      }
    }
  ]
}
```

### 配置验证

使用内置的配置验证器测试API连接：
1. 在浏览器中打开 `src/config-validator.html`
2. 查看配置概览和API连接状态
3. 点击"测试所有配置"验证API
4. 查看详细测试报告

## 🔧 故障排除

### 配置验证
使用配置验证器快速诊断问题：
```bash
# 在浏览器中打开
src/config-validator.html
```

### 常见问题

1. **配置验证失败**
   - 检查API地址格式是否正确
   - 验证Bearer Token是否有效
   - 确认网络连接正常

2. **加载项无法加载**
   - 检查manifest.xml中的URL
   - 确认Web服务器支持HTTPS
   - 验证SSL证书有效

3. **API调用失败**
   - 使用配置验证器测试API
   - 检查API服务器状态
   - 验证API密钥权限

### 调试方法
1. 按F12打开开发者工具
2. 查看Console标签的错误信息
3. 检查Network标签的API请求
4. 使用配置验证器分析问题

## 📚 部署指南

详细的部署说明请参考：[DEPLOYMENT.md](DEPLOYMENT.md)

包含以下内容：
- 快速部署步骤
- API配置说明
- 配置验证工具
- Web服务器配置
- 故障排除指南

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

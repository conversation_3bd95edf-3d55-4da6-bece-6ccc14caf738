{"version": 3, "file": "commands.js", "mappings": ";YACA,IAAIA,EAAsB,CAAC,E,aCA3B,IAAAC,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAAC,KAAAvB,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAAO,OAAAvB,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAQ,EAAAjB,EAAA,GAAAN,EAAA,GAAAI,EAAAmB,IAAArB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAG,IAAAnB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAqB,KAAAjB,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAqB,EAAAhB,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAQ,GAAA,GAAAT,EAAA,QAAAU,UAAA,oCAAAR,GAAA,IAAAD,GAAAK,EAAAL,EAAAQ,GAAAhB,EAAAQ,EAAAL,EAAAa,GAAAxB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAA0B,KAAAnB,EAAAI,IAAA,MAAAc,UAAA,wCAAAzB,EAAA2B,KAAA,OAAA3B,EAAAW,EAAAX,EAAA4B,MAAApB,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAsB,SAAA7B,EAAA0B,KAAAnB,GAAAC,EAAA,IAAAG,EAAAc,UAAA,oCAAApB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAyB,KAAAvB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAAa,MAAA5B,EAAA2B,KAAAV,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAoB,IAAA,UAAAC,IAAA,CAAA/B,EAAAY,OAAAoB,eAAA,IAAAxB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAoB,EAAAtB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAqB,eAAArB,OAAAqB,eAAAlC,EAAAgC,IAAAhC,EAAAmC,UAAAH,EAAAjB,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA+B,EAAArB,UAAAsB,EAAAjB,EAAAH,EAAA,cAAAoB,GAAAjB,EAAAiB,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAArB,EAAAiB,EAAA1B,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,EAAA,yBAAAW,EAAAH,EAAA,oDAAAyB,EAAA,kBAAAC,EAAA9B,EAAA+B,EAAAvB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAA2B,eAAA,IAAAhC,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAyC,QAAAvC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA2B,MAAAzB,EAAAsC,YAAAzC,EAAA0C,cAAA1C,EAAA2C,UAAA3C,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAA4C,EAAAzC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAAqB,KAAA,OAAAzB,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAoB,KAAA3B,EAAAW,GAAAkC,QAAAC,QAAAnC,GAAAoC,KAAA9C,EAAAI,EAAA,UAAA2C,EAAA7C,GAAA,sBAAAH,EAAA,KAAAD,EAAAkD,UAAA,WAAAJ,QAAA,SAAA5C,EAAAI,GAAA,IAAAe,EAAAjB,EAAA+C,MAAAlD,EAAAD,GAAA,SAAAoD,EAAAhD,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAA8C,EAAAC,EAAA,OAAAjD,EAAA,UAAAiD,EAAAjD,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAA8C,EAAAC,EAAA,QAAAjD,EAAA,CAAAgD,OAAA,MCDArD,EAAoBuD,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAOzD,GACR,GAAsB,iBAAX0D,OAAqB,OAAOA,MACxC,CACA,CAPuB,GDQxB,IAAIC,EAAS,KAOb,SAGeC,IAAU,OAAAC,EAAAV,MAAC,KAADD,UAAA,CAyBzB,SAAAW,IAFC,OAEDA,EAAAZ,EAAAZ,IAAAE,EAzBA,SAAAuB,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA7B,IAAAC,EAAA,SAAA6B,GAAA,cAAAA,EAAAlD,EAAAkD,EAAA/D,GAAA,OAMG2D,EAAA,EAAAC,EALqB,CACpB,cACA,YACA,eACA,cAGoC,YAAAD,EAAAC,EAAAxC,QAAA,CAAA2C,EAAA/D,EAAA,QAAjB,OAAV6D,EAAUD,EAAAD,GAAAI,EAAAlD,EAAA,EAAAkD,EAAA/D,EAAA,EAEMgE,MAAMH,GAAW,OAA1B,KAARC,EAAQC,EAAA/C,GACDiD,GAAI,CAAFF,EAAA/D,EAAA,eAAA+D,EAAA/D,EAAA,EACE8D,EAASI,OAAM,OAC8B,OAD5DX,EAAMQ,EAAA/C,EACNmD,QAAQC,IAAI,kCAADC,OAAmCR,IAAcE,EAAA9C,EAAA,UAAA8C,EAAA/D,EAAA,eAAA+D,EAAAlD,EAAA,EAAAkD,EAAA/C,EAAA,OAAA2C,IAAAI,EAAA/D,EAAA,eAQlEmE,QAAQG,KAAK,4DACb,cAAAP,EAAA9C,EAAA,KAAAyC,EAAA,kBACDX,MAAA,KAAAD,UAAA,CA6DD,SAMeyB,EAAOC,EAAAC,GAAA,OAAAC,EAAA3B,MAAC,KAADD,UAAA,CAmFtB,SAAA4B,IAFC,OAEDA,EAAA7B,EAAAZ,IAAAE,EAnFA,SAAAwC,EAAuBC,EAAMC,GAAO,IAAAC,EAAAC,EAAAC,EAAAlB,EAAAmB,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAApD,IAAAC,EAAA,SAAAoD,GAAA,cAAAA,EAAAzE,EAAAyE,EAAAtF,GAAA,OAsC/B,OAtC+BsF,EAAAzE,EAAA,EAE1BiE,EAAYD,EAAQU,IAE1BpB,QAAQC,IAAI,oBAADC,OAAqBS,EAAUU,MAC1CrB,QAAQC,IAAI,sBAADC,OAAuBS,EAAUW,QAEtCV,EAAYW,KAAKC,MAMrBX,EAFEF,EAAUU,IAAII,SAAS,uBAA+C,SAAtBd,EAAUe,QAE9C,CACZC,OAAQ,CAAC,EACTC,MAAOnB,EACPoB,cAAe,WACfC,gBAAiB,GACjBC,KAAMpB,EAAUoB,MAAQ,QAAUR,KAAKC,MACvCQ,mBAAoBrB,EAAUsB,mBAAoB,GAItC,CACZX,MAAOX,EAAUW,MACjBY,SAAU,CACR,CACEC,KAAM,SACNC,QAAS1B,EAAQ2B,cAEnB,CACEF,KAAM,OACNC,QAAS3B,IAGb6B,YAAa3B,EAAU2B,YACvBC,WAAY5B,EAAU6B,WAEzBrB,EAAAtF,EAAA,EAEsBgE,MAAMc,EAAUU,IAAK,CAC1CoB,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAiB,UAAFxC,OAAYS,EAAUgC,cAEvCC,KAAMC,KAAKC,UAAUjC,KACrB,OAG8E,GAV1ElB,EAAQwB,EAAAtE,EASRiE,EAAeS,KAAKC,MAAQZ,EAClCZ,QAAQC,IAAI,6BAADC,OAA8BP,EAASoD,OAAM,MAAA7C,OAAKY,EAAY,QAEpEnB,EAASG,GAAI,CAAFqB,EAAAtF,EAAA,eAAAsF,EAAAtF,EAAA,EACU8D,EAASc,OAAM,OACa,MAD9CM,EAASI,EAAAtE,EACfmD,QAAQgD,MAAM,yBAAD9C,OAA0Ba,IACjC,IAAIkC,MAAM,YAAD/C,OAAaP,EAASoD,SAAS,cAAA5B,EAAAtF,EAAA,EAG7B8D,EAASI,OAAM,OAIlC,GAJMiB,EAAIG,EAAAtE,EAENoE,EAAS,IAGTD,EAAKkC,OAAQ,CAAF/B,EAAAtF,EAAA,QAEboF,EAASD,EAAKkC,OAAOC,OAAOhC,EAAAtF,EAAA,oBACnBmF,EAAKoC,SAAWpC,EAAKoC,QAAQnG,OAAS,GAAC,CAAAkE,EAAAtF,EAAA,QAEhDoF,EAASD,EAAKoC,QAAQ,GAAGC,QAAQjB,QAAQe,OAAOhC,EAAAtF,EAAA,eAEe,MAA/DmE,QAAQgD,MAAM,0CAA2ChC,GACnD,IAAIiC,MAAM,eAAc,OAG6B,OAA7DjD,QAAQC,IAAI,0BAADC,OAA2Be,EAAOhE,OAAM,WAAUkE,EAAArE,EAAA,EACtDmE,GAAM,OAGyC,MAHzCE,EAAAzE,EAAA,EAAAwE,EAAAC,EAAAtE,EAGbmD,QAAQgD,MAAM,wBAAyB9B,EAAMmC,SAASnC,EAAA,cAAAC,EAAArE,EAAA,KAAA0D,EAAA,kBAGzD5B,MAAA,KAAAD,UAAA,CAnLD2E,OAAOC,QAAO7E,EAAAZ,IAAAE,EAAC,SAAAwF,IAAA,OAAA1F,IAAAC,EAAA,SAAA0F,GAAA,cAAAA,EAAA5H,GAAA,cAAA4H,EAAA5H,EAAA,EAEPwD,IAAY,cAAAoE,EAAA3G,EAAA,KAAA0G,EAAA,MAqLK,oBAATE,KACVA,KACkB,oBAAXvE,OACPA,YACkB,IAAXwE,EAAAA,EACPA,EAAAA,OACAC,GAMJC,aA9JF,SAAsBC,GACpBC,KAAKC,IAAG,eAAAC,EAAAvF,EAAAZ,IAAAE,EAAC,SAAAkG,EAAOC,GAAO,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA5G,IAAAC,EAAA,SAAA4G,GAAA,cAAAA,EAAAjI,EAAAiI,EAAA9I,GAAA,OAII,OAJJ8I,EAAAjI,EAAA,GAGb2H,EAAYF,EAAQS,SAASC,gBACzBC,KAAK,QAAQH,EAAA9I,EAAA,EAEjBsI,EAAQY,OAAM,UAEfV,EAAU5D,MAAkC,KAA1B4D,EAAU5D,KAAK0C,OAAa,CAAAwB,EAAA9I,EAAA,QAS/B,OAPlByH,OAAOa,QAAQa,GAAGC,mBAChB,uCACA,CAAEC,OAAQ,GAAIC,MAAO,IACrB,SAAUlE,GACR,GAGJ6C,EAAMsB,YAAYT,EAAA7H,EAAA,UAKsB,GAApCwH,EAAqB,QAATF,EAAGhF,SAAM,IAAAgF,GAAU,QAAVA,EAANA,EAAQiB,gBAAQ,IAAAjB,OAAA,EAAhBA,EAAmB,GACrB,CAAFO,EAAA9I,EAAA,cACT,IAAIoH,MAAM,kBAAiB,OAG4C,OAA/EjD,QAAQC,IAAI,0CAADC,OAA2CmE,EAAU5D,KAAKxD,SAAU0H,EAAA9I,EAAA,EACnDuE,EAAQiE,EAAU5D,KAAM6D,GAAa,OACoB,GAD/EC,EAAaI,EAAA9H,EACnBmD,QAAQC,IAAI,2CAADC,QAA4CqE,aAAa,EAAbA,EAAetH,SAAU,KAE5EsH,GAAiBA,IAAkBF,EAAU5D,KAAI,CAAAkE,EAAA9I,EAAA,QAEc,OAAjEwI,EAAUiB,WAAWf,EAAeR,KAAKwB,eAAeC,SAASb,EAAA9I,EAAA,EAC3DsI,EAAQY,OAAM,OAAAJ,EAAA9I,EAAA,eAAA8I,EAAAjI,EAAA,EAAAgI,EAAAC,EAAA9H,EAItBmD,QAAQgD,MAAM,WAAU0B,GAElBD,GAAqB,QAAND,EAAApF,SAAM,IAAAoF,GAAI,QAAJA,EAANA,EAAQQ,UAAE,IAAAR,GAAU,QAAVA,EAAVA,EAAYtC,gBAAQ,IAAAsC,OAAA,EAApBA,EAAsBiB,WAAYf,EAAMrB,QAC7DC,OAAOa,QAAQa,GAAGC,mBAAmB,4CAAD/E,OACUuE,EAAY,sBACxD,CAAES,OAAQ,GAAIC,MAAO,KACrB,OAGJrB,EAAMsB,YAAY,cAAAT,EAAA7H,EAAA,KAAAoH,EAAA,iBACnB,gBAAAwB,GAAA,OAAAzB,EAAArF,MAAA,KAAAD,UAAA,EAhDO,IAgDLgH,MAAM,SAAA3C,GACPhD,QAAQgD,MAAM,kBAAmBA,GACjCc,EAAMsB,WACR,EACF,C", "sources": ["webpack://word-deepseek-optimizer/webpack/bootstrap", "webpack://word-deepseek-optimizer/./src/commands/commands.js", "webpack://word-deepseek-optimizer/webpack/runtime/global"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "/*\n * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n * See LICENSE in the project root for license information.\n */\n\n/* global global, Office, self, window */\n\n// 全局配置变量\nlet config = null;\n\nOffice.onReady(async () => {\n  // 加载配置文件\n  await loadConfig();\n});\n\n/**\n * 加载配置文件\n */\nasync function loadConfig() {\n  const possiblePaths = [\n    './conf.json',\n    'conf.json',\n    '../conf.json',\n    '/conf.json'\n  ];\n\n  for (const configPath of possiblePaths) {\n    try {\n      const response = await fetch(configPath);\n      if (response.ok) {\n        config = await response.json();\n        console.log(`✅ Commands config loaded from: ${configPath}`);\n        return;\n      }\n    } catch (error) {\n      // 静默处理配置加载错误\n    }\n  }\n\n  console.warn(\"⚠️ Commands config not loaded, using basic functionality\");\n  // 不抛出错误，允许commands在没有配置的情况下使用基本功能\n}\n\n/**\n * Shows a notification when the add-in command is executed.\n * @param event {Office.AddinCommands.Event}\n */\nfunction optimizeText(event) {\n  Word.run(async (context) => {\n    try {\n      // 获取选中的文本\n      const selection = context.document.getSelection();\n      selection.load(\"text\");\n\n      await context.sync();\n\n      if (!selection.text || selection.text.trim() === \"\") {\n        // 显示任务窗格\n        Office.context.ui.displayDialogAsync(\n          'https://localhost:3001/taskpane.html',\n          { height: 60, width: 40 },\n          function (result) {\n            // 静默处理对话框结果\n          }\n        );\n        event.completed();\n        return;\n      }\n\n      // 使用配置文件中的第一个模式进行处理\n      const firstSection = config?.sections?.[0];\n      if (!firstSection) {\n        throw new Error(\"配置文件中没有可用的处理模式\");\n      }\n\n      console.log(`🚀 Commands API Request - Text length: ${selection.text.length}`);\n      const processedText = await callAPI(selection.text, firstSection);\n      console.log(`✅ Commands API Success - Result length: ${processedText?.length || 0}`);\n\n      if (processedText && processedText !== selection.text) {\n        // 替换选中的文本\n        selection.insertText(processedText, Word.InsertLocation.replace);\n        await context.sync();\n      }\n\n    } catch (error) {\n      console.error(\"处理文本时出错:\", error);\n      // 显示错误对话框\n      const errorMessage = config?.ui?.messages?.apiError || error.message;\n      Office.context.ui.displayDialogAsync(\n        `data:text/html,<html><body><h3>错误</h3><p>${errorMessage}</p></body></html>`,\n        { height: 30, width: 40 }\n      );\n    }\n\n    event.completed();\n  }).catch(error => {\n    console.error(\"Word.run error:\", error);\n    event.completed();\n  });\n}\n\n/**\n * 调用API处理文本\n * @param {string} text - 要处理的文本\n * @param {object} section - 配置段\n * @returns {Promise<string>} - 处理后的文本\n */\nasync function callAPI(text, section) {\n  try {\n    const apiConfig = section.api;\n\n    console.log(`🔗 Commands API: ${apiConfig.url}`);\n    console.log(`🤖 Commands Model: ${apiConfig.model}`);\n\n    const startTime = Date.now();\n\n    // 构建请求体 - 根据API类型选择不同格式\n    let requestBody;\n    if (apiConfig.url.includes('/api/chat-messages') || apiConfig.apiType === 'dify') {\n      // Dify API格式\n      requestBody = {\n        inputs: {},\n        query: text,\n        response_mode: \"blocking\",\n        conversation_id: \"\",\n        user: apiConfig.user || \"user-\" + Date.now(),\n        auto_generate_name: apiConfig.autoGenerateName || true\n      };\n    } else {\n      // OpenAI API格式\n      requestBody = {\n        model: apiConfig.model,\n        messages: [\n          {\n            role: 'system',\n            content: section.systemPrompt\n          },\n          {\n            role: 'user',\n            content: text\n          }\n        ],\n        temperature: apiConfig.temperature,\n        max_tokens: apiConfig.maxTokens\n      };\n    }\n\n    const response = await fetch(apiConfig.url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${apiConfig.bearerToken}`\n      },\n      body: JSON.stringify(requestBody)\n    });\n\n    const responseTime = Date.now() - startTime;\n    console.log(`📡 Commands API Response: ${response.status} (${responseTime}ms)`);\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(`❌ Commands API Error: ${errorText}`);\n      throw new Error(`API请求失败: ${response.status}`);\n    }\n\n    const data = await response.json();\n\n    let result = '';\n\n    // 检测API类型并解析相应格式\n    if (data.answer) {\n      // Dify API格式 - blocking模式直接返回answer\n      result = data.answer.trim();\n    } else if (data.choices && data.choices.length > 0) {\n      // OpenAI API格式\n      result = data.choices[0].message.content.trim();\n    } else {\n      console.error(`❌ Commands API: Invalid response format`, data);\n      throw new Error('API返回数据格式错误');\n    }\n\n    console.log(`✅ Commands API Result: ${result.length} chars`);\n    return result;\n\n  } catch (error) {\n    console.error('❌ Commands API Error:', error.message);\n    throw error;\n  }\n}\n\n// 注册函数供Office调用\nfunction getGlobal() {\n  return typeof self !== \"undefined\"\n    ? self\n    : typeof window !== \"undefined\"\n    ? window\n    : typeof global !== \"undefined\"\n    ? global\n    : undefined;\n}\n\nconst g = getGlobal();\n\n// The add-in command functions need to be available in global scope\ng.optimizeText = optimizeText;\n", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();"], "names": ["__webpack_require__", "e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "g", "globalThis", "this", "Function", "window", "config", "loadConfig", "_loadConfig", "_callee3", "_i", "_possiblePaths", "config<PERSON><PERSON>", "response", "_context3", "fetch", "ok", "json", "console", "log", "concat", "warn", "callAPI", "_x2", "_x3", "_callAPI", "_callee4", "text", "section", "apiConfig", "startTime", "requestBody", "responseTime", "errorText", "data", "result", "_t3", "_context4", "api", "url", "model", "Date", "now", "includes", "apiType", "inputs", "query", "response_mode", "conversation_id", "user", "auto_generate_name", "autoGenerateName", "messages", "role", "content", "systemPrompt", "temperature", "max_tokens", "maxTokens", "method", "headers", "bearerToken", "body", "JSON", "stringify", "status", "error", "Error", "answer", "trim", "choices", "message", "Office", "onReady", "_callee", "_context", "self", "global", "undefined", "optimizeText", "event", "Word", "run", "_ref2", "_callee2", "context", "_config", "selection", "firstSection", "processedText", "_config2", "errorMessage", "_t", "_context2", "document", "getSelection", "load", "sync", "ui", "displayDialogAsync", "height", "width", "completed", "sections", "insertText", "InsertLocation", "replace", "apiError", "_x", "catch"], "sourceRoot": ""}
/*
 * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
 * See LICENSE in the project root for license information.
 */

/* global global, Office, self, window */

// 全局配置变量
let config = null;

Office.onReady(async () => {
  // 加载配置文件
  await loadConfig();
});

/**
 * 加载配置文件
 */
async function loadConfig() {
  const possiblePaths = [
    './conf.json',
    'conf.json',
    '../conf.json',
    '/conf.json'
  ];

  for (const configPath of possiblePaths) {
    try {
      const response = await fetch(configPath);
      if (response.ok) {
        config = await response.json();
        console.log(`✅ Commands config loaded from: ${configPath}`);
        return;
      }
    } catch (error) {
      // 静默处理配置加载错误
    }
  }

  console.warn("⚠️ Commands config not loaded, using basic functionality");
  // 不抛出错误，允许commands在没有配置的情况下使用基本功能
}

/**
 * Shows a notification when the add-in command is executed.
 * @param event {Office.AddinCommands.Event}
 */
function optimizeText(event) {
  Word.run(async (context) => {
    try {
      // 获取选中的文本
      const selection = context.document.getSelection();
      selection.load("text");

      await context.sync();

      if (!selection.text || selection.text.trim() === "") {
        // 显示任务窗格
        Office.context.ui.displayDialogAsync(
          'https://localhost:3001/taskpane.html',
          { height: 60, width: 40 },
          function (result) {
            // 静默处理对话框结果
          }
        );
        event.completed();
        return;
      }

      // 使用配置文件中的第一个模式进行处理
      const firstSection = config?.sections?.[0];
      if (!firstSection) {
        throw new Error("配置文件中没有可用的处理模式");
      }

      console.log(`🚀 Commands API Request - Text length: ${selection.text.length}`);
      const processedText = await callAPI(selection.text, firstSection);
      console.log(`✅ Commands API Success - Result length: ${processedText?.length || 0}`);

      if (processedText && processedText !== selection.text) {
        // 替换选中的文本
        selection.insertText(processedText, Word.InsertLocation.replace);
        await context.sync();
      }

    } catch (error) {
      console.error("处理文本时出错:", error);
      // 显示错误对话框
      const errorMessage = config?.ui?.messages?.apiError || error.message;
      Office.context.ui.displayDialogAsync(
        `data:text/html,<html><body><h3>错误</h3><p>${errorMessage}</p></body></html>`,
        { height: 30, width: 40 }
      );
    }

    event.completed();
  }).catch(error => {
    console.error("Word.run error:", error);
    event.completed();
  });
}

/**
 * 调用API处理文本
 * @param {string} text - 要处理的文本
 * @param {object} section - 配置段
 * @returns {Promise<string>} - 处理后的文本
 */
async function callAPI(text, section) {
  try {
    const apiConfig = section.api;

    console.log(`🔗 Commands API: ${apiConfig.url}`);
    console.log(`🤖 Commands Model: ${apiConfig.model}`);

    const startTime = Date.now();

    // 构建请求体 - 根据API类型选择不同格式
    let requestBody;
    if (apiConfig.url.includes('/api/chat-messages') || apiConfig.apiType === 'dify') {
      // Dify API格式
      requestBody = {
        inputs: {},
        query: text,
        response_mode: "blocking",
        conversation_id: "",
        user: apiConfig.user || "user-" + Date.now(),
        auto_generate_name: apiConfig.autoGenerateName || true
      };
    } else {
      // OpenAI API格式
      requestBody = {
        model: apiConfig.model,
        messages: [
          {
            role: 'system',
            content: section.systemPrompt
          },
          {
            role: 'user',
            content: text
          }
        ],
        temperature: apiConfig.temperature,
        max_tokens: apiConfig.maxTokens
      };
    }

    const response = await fetch(apiConfig.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiConfig.bearerToken}`
      },
      body: JSON.stringify(requestBody)
    });

    const responseTime = Date.now() - startTime;
    console.log(`📡 Commands API Response: ${response.status} (${responseTime}ms)`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Commands API Error: ${errorText}`);
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();

    let result = '';

    // 检测API类型并解析相应格式
    if (data.answer) {
      // Dify API格式 - blocking模式直接返回answer
      result = data.answer.trim();
    } else if (data.choices && data.choices.length > 0) {
      // OpenAI API格式
      result = data.choices[0].message.content.trim();
    } else {
      console.error(`❌ Commands API: Invalid response format`, data);
      throw new Error('API返回数据格式错误');
    }

    console.log(`✅ Commands API Result: ${result.length} chars`);
    return result;

  } catch (error) {
    console.error('❌ Commands API Error:', error.message);
    throw error;
  }
}

// 注册函数供Office调用
function getGlobal() {
  return typeof self !== "undefined"
    ? self
    : typeof window !== "undefined"
    ? window
    : typeof global !== "undefined"
    ? global
    : undefined;
}

const g = getGlobal();

// The add-in command functions need to be available in global scope
g.optimizeText = optimizeText;

# API格式更新说明

## 🔄 更新内容

根据真实的Dify API请求格式，已对代码进行以下更新：

### 1. **Dify API请求格式修正**

**原格式**：
```json
{
  "inputs": {},
  "query": "用户消息",
  "response_mode": "streaming",
  "conversation_id": "",
  "user": "user-default",
  "auto_generate_name": true
}
```

**新格式（真实格式）**：
```json
{
  "response_mode": "streaming",
  "conversation_id": "",
  "query": "用户消息",
  "inputs": {}
}
```

### 2. **代码修改位置**

#### taskpane.js
- **行670-698**: 修改了Dify API请求体构建逻辑
- **行703-713**: 优化了请求头设置

#### config-validator.html
- **重新创建**: 使用正确的Dify API格式进行测试
- **行344-616**: 添加了完整的API测试功能

### 3. **配置文件格式**

**Dify API配置示例**：
```json
{
  "id": "expand",
  "name": "📖 扩写",
  "api": {
    "url": "http://17.22.40.148:6080/api/chat-messages",
    "bearerToken": "eyJhbGciOiJIUzI1NiIs...",
    "model": "dify-chat",
    "responseMode": "streaming",
    "apiType": "dify"
  }
}
```

**OpenAI API配置示例**：
```json
{
  "id": "optimize",
  "name": "📝 优化",
  "api": {
    "url": "https://tbai.xin/v1/chat/completions",
    "bearerToken": "sk-WPcYhmgAgknnL1hl...",
    "model": "deepseek-r1",
    "responseMode": "streaming"
  }
}
```

### 4. **主要改进**

#### ✅ 请求格式标准化
- 移除了不必要的字段（`user`, `auto_generate_name`）
- 调整了字段顺序以匹配真实API
- 简化了请求体结构

#### ✅ 配置验证器增强
- 支持真实的Dify API格式测试
- 改进了错误诊断功能
- 添加了详细的测试日志

#### ✅ 代码优化
- 统一了请求头设置逻辑
- 改进了API类型检测
- 增强了错误处理

### 5. **测试验证**

#### 使用配置验证器
1. 打开 `src/config-validator.html`
2. 查看配置概览
3. 点击"测试所有配置"
4. 检查测试结果

#### 预期结果
- ✅ 配置完整性检查通过
- ✅ API连接测试成功
- ✅ 响应格式验证正确

### 6. **部署注意事项**

#### 🔧 配置更新
- 确保Dify API配置包含 `"apiType": "dify"`
- 验证Bearer Token格式正确
- 检查API URL可访问性

#### 🧪 测试流程
1. 使用配置验证器测试所有API
2. 在Word中测试实际功能
3. 验证流式响应正常工作

#### 🚀 生产部署
1. 运行 `npm run build`
2. 部署 `dist/` 目录到Web服务器
3. 确保HTTPS配置正确
4. 更新manifest.xml中的域名

### 7. **故障排除**

#### 常见问题
- **连接失败**: 检查API URL和网络连接
- **认证错误**: 验证Bearer Token有效性
- **格式错误**: 使用配置验证器诊断

#### 调试工具
- **配置验证器**: `src/config-validator.html`
- **浏览器控制台**: F12 > Console
- **网络请求**: F12 > Network

---

**更新完成时间**: 2025年7月29日  
**影响范围**: Dify API集成、配置验证、错误处理  
**向后兼容**: ✅ 完全兼容现有OpenAI API配置

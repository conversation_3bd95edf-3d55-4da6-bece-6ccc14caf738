/*! For license information please see commands.js.LICENSE.txt */
!function(){var n={};function e(){var n,o,r="function"==typeof Symbol?Symbol:{},c=r.iterator||"@@iterator",a=r.toStringTag||"@@toStringTag";function i(e,r,c,a){var i=r&&r.prototype instanceof u?r:u,l=Object.create(i.prototype);return t(l,"_invoke",function(e,t,r){var c,a,i,u=0,l=r||[],f=!1,p={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(e,t){return c=e,a=0,i=n,p.n=t,s}};function d(e,t){for(a=e,i=t,o=0;!f&&u&&!r&&o<l.length;o++){var r,c=l[o],d=p.p,m=c[2];e>3?(r=m===t)&&(i=c[(a=c[4])?5:(a=3,3)],c[4]=c[5]=n):c[0]<=d&&((r=e<2&&d<c[1])?(a=0,p.v=t,p.n=c[1]):d<m&&(r=e<3||c[0]>t||t>m)&&(c[4]=e,c[5]=t,p.n=m,a=0))}if(r||e>1)return s;throw f=!0,t}return function(r,l,m){if(u>1)throw TypeError("Generator is already running");for(f&&1===l&&d(l,m),a=l,i=m;(o=a<2?n:i)||!f;){c||(a?a<3?(a>1&&(p.n=-1),d(a,i)):p.n=i:p.v=i);try{if(u=2,c){if(a||(r="next"),o=c[r]){if(!(o=o.call(c,i)))throw TypeError("iterator result is not an object");if(!o.done)return o;i=o.value,a<2&&(a=0)}else 1===a&&(o=c.return)&&o.call(c),a<2&&(i=TypeError("The iterator does not provide a '"+r+"' method"),a=1);c=n}else if((o=(f=p.n<0)?i:e.call(t,p))!==s)break}catch(e){c=n,a=1,i=e}finally{u=1}}return{value:o,done:f}}}(e,c,a),!0),l}var s={};function u(){}function l(){}function f(){}o=Object.getPrototypeOf;var p=[][c]?o(o([][c]())):(t(o={},c,function(){return this}),o),d=f.prototype=u.prototype=Object.create(p);function m(n){return Object.setPrototypeOf?Object.setPrototypeOf(n,f):(n.__proto__=f,t(n,a,"GeneratorFunction")),n.prototype=Object.create(d),n}return l.prototype=f,t(d,"constructor",f),t(f,"constructor",l),l.displayName="GeneratorFunction",t(f,a,"GeneratorFunction"),t(d),t(d,a,"Generator"),t(d,c,function(){return this}),t(d,"toString",function(){return"[object Generator]"}),(e=function(){return{w:i,m:m}})()}function t(n,e,o,r){var c=Object.defineProperty;try{c({},"",{})}catch(n){c=0}t=function(n,e,o,r){function a(e,o){t(n,e,function(n){return this._invoke(e,o,n)})}e?c?c(n,e,{value:o,enumerable:!r,configurable:!r,writable:!r}):n[e]=o:(a("next",0),a("throw",1),a("return",2))},t(n,e,o,r)}function o(n,e,t,o,r,c,a){try{var i=n[c](a),s=i.value}catch(n){return void t(n)}i.done?e(s):Promise.resolve(s).then(o,r)}function r(n){return function(){var e=this,t=arguments;return new Promise(function(r,c){var a=n.apply(e,t);function i(n){o(a,r,c,i,s,"next",n)}function s(n){o(a,r,c,i,s,"throw",n)}i(void 0)})}}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(n){if("object"==typeof window)return window}}();var c=null;function a(){return i.apply(this,arguments)}function i(){return(i=r(e().m(function n(){var t,o,r,a;return e().w(function(n){for(;;)switch(n.p=n.n){case 0:t=0,o=["./conf.json","conf.json","../conf.json","/conf.json"];case 1:if(!(t<o.length)){n.n=8;break}return r=o[t],n.p=2,n.n=3,fetch(r);case 3:if(!(a=n.v).ok){n.n=5;break}return n.n=4,a.json();case 4:return c=n.v,console.log("✅ Commands config loaded from: ".concat(r)),n.a(2);case 5:n.n=7;break;case 6:n.p=6,n.v;case 7:t++,n.n=1;break;case 8:console.warn("⚠️ Commands config not loaded, using basic functionality");case 9:return n.a(2)}},n,null,[[2,6]])}))).apply(this,arguments)}function s(n,e){return u.apply(this,arguments)}function u(){return(u=r(e().m(function n(t,o){var r,c,a,i,s,u,l,f,p;return e().w(function(n){for(;;)switch(n.p=n.n){case 0:return n.p=0,r=o.api,console.log("🔗 Commands API: ".concat(r.url)),console.log("🤖 Commands Model: ".concat(r.model)),c=Date.now(),a=r.url.includes("/api/chat-messages")||"dify"===r.apiType?{inputs:{},query:t,response_mode:"blocking",conversation_id:"",user:r.user||"user-"+Date.now(),auto_generate_name:r.autoGenerateName||!0}:{model:r.model,messages:[{role:"system",content:o.systemPrompt},{role:"user",content:t}],temperature:r.temperature,max_tokens:r.maxTokens},n.n=1,fetch(r.url,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.bearerToken)},body:JSON.stringify(a)});case 1:if(i=n.v,s=Date.now()-c,console.log("📡 Commands API Response: ".concat(i.status," (").concat(s,"ms)")),i.ok){n.n=3;break}return n.n=2,i.text();case 2:throw u=n.v,console.error("❌ Commands API Error: ".concat(u)),new Error("API请求失败: ".concat(i.status));case 3:return n.n=4,i.json();case 4:if(l=n.v,f="",!l.answer){n.n=5;break}f=l.answer.trim(),n.n=7;break;case 5:if(!(l.choices&&l.choices.length>0)){n.n=6;break}f=l.choices[0].message.content.trim(),n.n=7;break;case 6:throw console.error("❌ Commands API: Invalid response format",l),new Error("API返回数据格式错误");case 7:return console.log("✅ Commands API Result: ".concat(f.length," chars")),n.a(2,f);case 8:throw n.p=8,p=n.v,console.error("❌ Commands API Error:",p.message),p;case 9:return n.a(2)}},n,null,[[0,8]])}))).apply(this,arguments)}Office.onReady(r(e().m(function n(){return e().w(function(n){for(;;)switch(n.n){case 0:return n.n=1,a();case 1:return n.a(2)}},n)}))),("undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:void 0).optimizeText=function(n){Word.run(function(){var t=r(e().m(function t(o){var r,a,i,u,l,f,p;return e().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,(a=o.document.getSelection()).load("text"),e.n=1,o.sync();case 1:if(a.text&&""!==a.text.trim()){e.n=2;break}return Office.context.ui.displayDialogAsync("https://localhost:3001/taskpane.html",{height:60,width:40},function(n){}),n.completed(),e.a(2);case 2:if(i=null===(r=c)||void 0===r||null===(r=r.sections)||void 0===r?void 0:r[0]){e.n=3;break}throw new Error("配置文件中没有可用的处理模式");case 3:return console.log("🚀 Commands API Request - Text length: ".concat(a.text.length)),e.n=4,s(a.text,i);case 4:if(u=e.v,console.log("✅ Commands API Success - Result length: ".concat((null==u?void 0:u.length)||0)),!u||u===a.text){e.n=5;break}return a.insertText(u,Word.InsertLocation.replace),e.n=5,o.sync();case 5:e.n=7;break;case 6:e.p=6,p=e.v,console.error("处理文本时出错:",p),f=(null===(l=c)||void 0===l||null===(l=l.ui)||void 0===l||null===(l=l.messages)||void 0===l?void 0:l.apiError)||p.message,Office.context.ui.displayDialogAsync("data:text/html,<html><body><h3>错误</h3><p>".concat(f,"</p></body></html>"),{height:30,width:40});case 7:n.completed();case 8:return e.a(2)}},t,null,[[0,6]])}));return function(n){return t.apply(this,arguments)}}()).catch(function(e){console.error("Word.run error:",e),n.completed()})}}();
//# sourceMappingURL=commands.js.map
{"version": 3, "file": "taskpane.js", "mappings": ";8gCACA,IAAAA,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAAC,KAAAvB,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAAO,OAAAvB,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAQ,EAAAjB,EAAA,GAAAN,EAAA,GAAAI,EAAAmB,IAAArB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAG,IAAAnB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAqB,KAAAjB,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAqB,EAAAhB,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAQ,GAAA,GAAAT,EAAA,QAAAU,UAAA,oCAAAR,GAAA,IAAAD,GAAAK,EAAAL,EAAAQ,GAAAhB,EAAAQ,EAAAL,EAAAa,GAAAxB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAA0B,KAAAnB,EAAAI,IAAA,MAAAc,UAAA,wCAAAzB,EAAA2B,KAAA,OAAA3B,EAAAW,EAAAX,EAAA4B,MAAApB,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAsB,SAAA7B,EAAA0B,KAAAnB,GAAAC,EAAA,IAAAG,EAAAc,UAAA,oCAAApB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAyB,KAAAvB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAAa,MAAA5B,EAAA2B,KAAAV,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAoB,IAAA,UAAAC,IAAA,CAAA/B,EAAAY,OAAAoB,eAAA,IAAAxB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAoB,EAAAtB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAqB,eAAArB,OAAAqB,eAAAlC,EAAAgC,IAAAhC,EAAAmC,UAAAH,EAAAjB,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA+B,EAAArB,UAAAsB,EAAAjB,EAAAH,EAAA,cAAAoB,GAAAjB,EAAAiB,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAArB,EAAAiB,EAAA1B,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,EAAA,yBAAAW,EAAAH,EAAA,oDAAAyB,EAAA,kBAAAC,EAAA9B,EAAA+B,EAAAvB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAA2B,eAAA,IAAAhC,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAyC,QAAAvC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA2B,MAAAzB,EAAAsC,YAAAzC,EAAA0C,cAAA1C,EAAA2C,UAAA3C,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAA4C,EAAAzC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAAqB,KAAA,OAAAzB,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAoB,KAAA3B,EAAAW,GAAAkC,QAAAC,QAAAnC,GAAAoC,KAAA9C,EAAAI,EAAA,UAAA2C,EAAA7C,GAAA,sBAAAH,EAAA,KAAAD,EAAAkD,UAAA,WAAAJ,QAAA,SAAA5C,EAAAI,GAAA,IAAAe,EAAAjB,EAAA+C,MAAAlD,EAAAD,GAAA,SAAAoD,EAAAhD,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAA8C,EAAAC,EAAA,OAAAjD,EAAA,UAAAiD,EAAAjD,GAAAyC,EAAAxB,EAAAnB,EAAAI,EAAA8C,EAAAC,EAAA,QAAAjD,EAAA,CAAAgD,OAAA,MAOA,IAAIE,EAAS,KACTC,EAAc,WACdC,EAAe,GACfC,EAAgB,GAChBC,EAAmB,KAKvB,SAASC,IACP,OAAO,IAAIb,QAAQ,SAACC,GAClB,GAAsB,oBAAXa,QAA0BA,OAAOC,QAC1Cd,QACK,CAEL,IAAMe,EAAc,WACI,oBAAXF,QAA0BA,OAAOC,QAC1Cd,IAEAgB,WAAWD,EAAa,IAE5B,EACAC,WAAWD,EAAa,IAC1B,CACF,EACF,CAmCA,SAAAE,IAFC,OAEDA,EAAAf,EAAAZ,IAAAE,EAhCA,SAAA0B,IAAA,IAAAC,EAAA,OAAA7B,IAAAC,EAAA,SAAA6B,GAAA,cAAAA,EAAAlD,EAAAkD,EAAA/D,GAAA,OAC4C,OAA1CgE,QAAQC,IAAI,8BAA8BF,EAAAlD,EAAA,EAAAkD,EAAA/D,EAAA,EAIlC0C,QAAQwB,KAAK,CACjBX,IACA,IAAIb,QAAQ,SAACyB,EAAGC,GAAM,OAAKT,WAAW,kBAAMS,EAAO,IAAIC,MAAM,qBAAqB,EAAE,IAAK,KACzF,OAEFL,QAAQC,IAAI,+CAEZT,OAAOC,QAAQ,SAACa,GACdN,QAAQC,IAAI,+BAAgCK,GAGhB,YAAxBC,SAASC,WACXD,SAASE,iBAAiB,mBAAoBC,GAE9CA,GAEJ,GAAGC,MAAM,SAAAC,GACPZ,QAAQY,MAAM,wBAAyBA,GACvCC,GACF,GAAGd,EAAA/D,EAAA,eAAA+D,EAAAlD,EAAA,EAAAiD,EAAAC,EAAA/C,EAGHgD,QAAQc,KAAK,sCAAqChB,GAClDe,IAAwB,cAAAd,EAAA9C,EAAA,KAAA4C,EAAA,kBAE3Bd,MAAA,KAAAD,UAAA,CAGD,SAAS+B,IACPb,QAAQC,IAAI,wDACgB,YAAxBM,SAASC,WACXD,SAASE,iBAAiB,mBAAoBC,GAE9CA,GAEJ,CAKA,SAGeA,IAAa,OAAAK,EAAAhC,MAAC,KAADD,UAAA,CA0C5B,SAAAiC,IAFC,OAEDA,EAAAlC,EAAAZ,IAAAE,EA1CA,SAAA6C,IAAA,IAAAC,EAAAC,EAAA,OAAAjD,IAAAC,EAAA,SAAAiD,GAAA,cAAAA,EAAAtE,EAAAsE,EAAAnF,GAAA,OAQI,OAPFgE,QAAQC,IAAI,uBAAuBkB,EAAAtE,EAAA,EAIjCmD,QAAQC,IAAI,iBAAoC,oBAAXT,OAAyBA,OAAS,aACvEQ,QAAQC,IAAI,eAAgC,oBAATmB,KAAuBA,KAAO,aAEjED,EAAAnF,EAAA,EACMqF,IAAY,OAGlBC,IAGAC,IAAaJ,EAAAnF,EAAA,eAAAmF,EAAAtE,EAAA,EAAAqE,EAAAC,EAAAnE,EAEbgD,QAAQY,MAAM,4BAA2BM,GAGzC,KACQD,EAAgBV,SAASiB,eAAe,aAE5CP,EAAcQ,YAAc,cAAHC,OAAiBR,EAAMS,SAChDV,EAAcW,UAAY,eAC1BX,EAAcY,MAAMC,QAAU,SAIT,CAAC,YAAa,uBAAwB,eAAgB,kBAC9DC,QAAQ,SAAAC,GACrB,IAAMC,EAAU1B,SAASiB,eAAeQ,GACpCC,IACFA,EAAQJ,MAAMC,QAAU,OAE5B,EACF,CAAE,MAAOI,GACPlC,QAAQY,MAAM,yBAA0BsB,EAC1C,CAAC,cAAAf,EAAAlE,EAAA,KAAA+D,EAAA,kBAEJjC,MAAA,KAAAD,UAAA,UAKcuC,IAAU,OAAAc,EAAApD,MAAC,KAADD,UAAA,CAyCzB,SAAAqD,IAJC,OAIDA,EAAAtD,EAAAZ,IAAAE,EAzCA,SAAAiE,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA3E,IAAAC,EAAA,SAAA2E,GAAA,cAAAA,EAAAhG,EAAAgG,EAAA7G,GAAA,OASMsG,EAAY,KAAIC,EAAA,EAAAC,EAPE,CACpB,cACA,YACA,eACA,cAKoC,YAAAD,EAAAC,EAAApF,QAAA,CAAAyF,EAAA7G,EAAA,SAAjB,OAAVyG,EAAUD,EAAAD,GAAAM,EAAAhG,EAAA,EAAAgG,EAAA7G,EAAA,EAEM8G,MAAML,GAAW,OAA1B,KAARC,EAAQG,EAAA7F,GAED+F,GAAI,CAAFF,EAAA7G,EAAA,eAAA6G,EAAA7G,EAAA,EACY0G,EAASM,OAAM,OAGxC,GAHML,EAAUE,EAAA7F,GAChBkC,EAAS+D,KAAKC,MAAMP,IAGRQ,UAAaC,MAAMC,QAAQnE,EAAOiE,WAAwC,IAA3BjE,EAAOiE,SAAS/F,OAAY,CAAAyF,EAAA7G,EAAA,cAC/E,IAAIqE,MAAM,qDAAoD,OAGgB,OAAtFL,QAAQC,IAAI,oBAADyB,OAAqBxC,EAAOiE,SAAS/F,OAAM,mBAAAsE,OAAkBe,IAAcI,EAAA5F,EAAA,UAGtFqF,EAAY,IAAIjC,MAAM,QAADqB,OAASgB,EAASY,OAAM,SAAA5B,OAAQe,IAAc,OAAAI,EAAA7G,EAAA,eAAA6G,EAAAhG,EAAA,EAAA+F,EAAAC,EAAA7F,EAGrEsF,EAASM,EAAS,OAAAL,IAAAM,EAAA7G,EAAA,gBAKkC,MAAxDgE,QAAQY,MAAM,0CACR,IAAIP,MAAM,aAADqB,QAAuB,QAATW,EAAAC,SAAS,IAAAD,OAAA,EAATA,EAAWV,UAAW,cAAc,eAAAkB,EAAA5F,EAAA,KAAAmF,EAAA,kBAClErD,MAAA,KAAAD,UAAA,CAOD,SAASwC,IAAe,IAAAiC,EACtB,GAAKrE,EAKL,GAAKA,EAAOiE,UAAaC,MAAMC,QAAQnE,EAAOiE,UAA9C,CAMA,IAAMK,EAASjD,SAASiB,eAAe,UACnCgC,GAAUtE,EAAOuE,IACnBD,EAAOE,UAAY,eAAHhC,OACRxC,EAAOuE,IAAIE,MAAK,oBAAAjC,OACjBxC,EAAOuE,IAAIG,SAAQ,cAG1B5D,QAAQY,MAAM,oDAIhB,IAAMiD,EAAWtD,SAASiB,eAAe,aACzC,GAAKqC,EAAL,CAKA7D,QAAQC,IAAI,oCAAqCf,EAAOiE,SAASW,IAAI,SAAAC,GAAC,OAAIA,EAAEC,IAAI,IAChFH,EAASH,UAAY,GAErBxE,EAAOiE,SAASpB,QAAQ,SAACkC,EAASC,GAChClE,QAAQC,IAAI,oBAADyB,OAAqBwC,EAAQ,EAAC,MAAAxC,OAAKuC,EAAQD,KAAI,MAAAtC,OAAKuC,EAAQjC,GAAE,MAEzE,IAAMmC,EAAM5D,SAAS6D,cAAc,UACnCD,EAAInC,GAAK,GAAHN,OAAMuC,EAAQjC,GAAE,QACtBmC,EAAIvC,UAAY,YAAHF,OAAyB,IAAVwC,EAAc,SAAW,IACrDC,EAAI1C,YAAcwC,EAAQD,KAC1BG,EAAIE,QAAU,WACZC,EAAWL,EAAQjC,GACrB,EACA6B,EAASU,YAAYJ,EACvB,GAEAnE,QAAQC,IAAI,aAADyB,OAAcxC,EAAOiE,SAAS/F,OAAM,UAG3C8B,EAAOiE,SAAS/F,OAAS,IAC3B+B,EAAcD,EAAOiE,SAAS,GAAGnB,GACjChC,QAAQC,IAAI,2BAADyB,OAA4BvC,KAIzC,IAAMqF,EAAejE,SAASiB,eAAe,gBAC7C,GAAIgD,GAAyB,QAAbjB,EAAIrE,EAAOuF,UAAE,IAAAlB,GAATA,EAAWiB,aAAc,CAC3C,IAAME,EAAqBxF,EAAOuF,GAAGD,aACrCA,EAAad,UAAY,eAAHhC,OACdgD,EAAmBf,MAAK,+BAAAjC,OAE1BgD,EAAmBC,MAAMb,IAAI,SAAAc,GAAI,aAAAlD,OAAWkD,EAAI,WAASC,KAAK,IAAG,sBAGzE,MACE7E,QAAQc,KAAK,+CACT0D,IACFA,EAAa3C,MAAMC,QAAU,QAKjCgD,GA5CA,MAFE9E,QAAQY,MAAM,gCAhBhB,MAFEZ,QAAQY,MAAM,qCAAsC1B,EAAOiE,eAL3DnD,QAAQY,MAAM,iCAsElB,CAKA,SAASW,IAEP,IAAMwD,EAAgBxE,SAASiB,eAAe,kBAC1CuD,EACFA,EAAcV,QAAUW,EAExBhF,QAAQY,MAAM,2BAElB,CAMA,SAAS0D,EAAWW,GAClB9F,EAAc8F,EAGd1E,SAAS2E,iBAAiB,aAAanD,QAAQ,SAAAoC,GAC7CA,EAAIgB,UAAUC,OAAO,SACvB,GAEA,IAAMC,EAAY9E,SAASiB,eAAe,GAADE,OAAIuD,EAAI,SAC7CI,IACFA,EAAUF,UAAUG,IAAI,UACxBtF,QAAQC,IAAI,8BAADyB,OAA+B2D,EAAUrD,MAItD8C,IAGA,IAAMS,EAAiBC,IACjBC,EAAqBlF,SAASiB,eAAe,wBAEnD,GAAI+D,GAAkBA,EAAeG,gBAAiB,CACpDD,EAAmB5D,MAAMC,QAAU,QACnC,IAAM6D,EAAcpF,SAASiB,eAAe,gBACxCmE,GAAeJ,EAAeK,yBAChCD,EAAYE,YAAcN,EAAeK,uBAE7C,MACEH,EAAmB5D,MAAMC,QAAU,MAEvC,CAKA,SAASgD,IACP,IAAMgB,EAAavF,SAASiB,eAAe,eACrC+D,EAAiBC,IAEnBM,GAAcP,IAChBO,EAAWrE,YAAc8D,EAAeO,WACxC9F,QAAQC,IAAI,2BAADyB,OAA4B6D,EAAeO,aAE1D,CAKA,SAASN,IACP,OAAKtG,GAAWA,EAAOiE,SAChBjE,EAAOiE,SAAS4C,KAAK,SAAA9B,GAAO,OAAIA,EAAQjC,KAAO7C,CAAW,GADzB,IAE1C,CAOA,SAGe6F,IAAmB,OAAAgB,EAAAjH,MAAC,KAADD,UAAA,CAoJlC,SAAAkH,IAFC,OAEDA,EAAAnH,EAAAZ,IAAAE,EApJA,SAAA8H,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAhD,EAAAiD,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA3I,IAAAC,EAAA,SAAA2I,GAAA,cAAAA,EAAAhK,EAAAgK,EAAA7K,GAAA,OA4BI,GA3BFgE,QAAQC,IAAI,4CAA6Cd,GAEnD+G,EAAS3F,SAASiB,eAAe,kBACjC2E,EAAkB5F,SAASiB,eAAe,oBAC1C4E,EAAe7F,SAASiB,eAAe,iBACvC6E,EAAiB9F,SAASiB,eAAe,mBACzC8E,EAAc/F,SAASiB,eAAe,gBACtC8B,EAAS/C,SAASiB,eAAe,UAEnC+E,GAAU,EACRC,EAAYM,KAAKC,MAAKF,EAAAhK,EAAA,EAI1BwJ,EAAexE,MAAMC,QAAU,OAC/BwE,EAAYzE,MAAMC,QAAU,OAC5BwB,EAAOzB,MAAMC,QAAU,OAGvBoE,EAAOc,UAAW,EAClBb,EAAgBtE,MAAMC,QAAU,QAChCsE,EAAavE,MAAMC,QAAU,QAG7BmF,EAAe,EAAG,UAClBC,IAGoB,oBAAT9F,OAAwBA,KAAK+F,IAAG,CAAAN,EAAA7K,EAAA,eAAA6K,EAAA7K,EAAA,EAEnCoF,KAAK+F,IAAG,eAAAC,EAAAvI,EAAAZ,IAAAE,EAAC,SAAAkJ,EAAOC,GAAO,IAAAC,EAAAC,EAAAb,EAAAc,EAAAC,EAAAC,EAAAC,EAAArC,EAAAsC,EAAA,OAAA5J,IAAAC,EAAA,SAAA4J,GAAA,cAAAA,EAAA9L,GAAA,OAGJ,OADjBuL,EAAYD,EAAQ/G,SAASwH,gBACzBC,KAAK,QAAQF,EAAA9L,EAAA,EAEjBsL,EAAQW,OAAM,UAGfC,EAAaX,EAAUvE,MAAO,CAAF8E,EAAA9L,EAAA,QACY,MAArC2K,GAAiB,QAANa,EAAAtI,SAAM,IAAAsI,GAAI,QAAJA,EAANA,EAAQ/C,UAAE,IAAA+C,OAAA,EAAVA,EAAYb,WAAY,CAAC,EACpC,IAAItG,MAAMsG,EAASwB,aAAe,eAAc,OASxD,OALA/I,EAAemI,EAAUvE,KACzB1D,EAAmBiI,EAEnBN,EAAe,GAAI,aAEnBa,EAAA9L,EAAA,EACwBoM,EAAab,EAAUvE,KAAM7D,GAAY,QAA3DsI,EAASK,EAAA9K,IAEEyK,IAAcF,EAAUvE,MACvCiE,EAAe,IAAK,SAGpB5H,EAAgBoI,EAGhBtB,EAAgBtE,MAAMC,QAAU,OAChCsE,EAAavE,MAAMC,QAAU,OAG7BuG,EAAYhJ,GAENsI,EAAWb,KAAKC,MAAQP,EACxBG,GAAiB,QAANe,EAAAxI,SAAM,IAAAwI,GAAI,QAAJA,EAANA,EAAQjD,UAAE,IAAAiD,OAAA,EAAVA,EAAYf,WAAY,CAAC,EACpCpB,EAAiBC,IACjBqC,GAAatC,aAAc,EAAdA,EAAgBvB,OAAQ,KAC3CsE,EAAW,KAAD5G,OAAMmG,GAAUnG,OAAGiF,EAASJ,SAAW,MAAK,SAAA7E,OAAQ6G,KAAKC,MAAMb,EAAS,KAAK,MAAM,QAE7FpB,GAAU,IAEVU,EAAe,IAAK,QACpBqB,EAAW,qBAAsB,WACjC/B,GAAU,GACX,cAAAuB,EAAA7K,EAAA,KAAAoK,EAAA,IACF,gBAAAoB,GAAA,OAAArB,EAAArI,MAAA,KAAAD,UAAA,EA/Ca,IA+CZ,OAAA+H,EAAA7K,EAAA,eAGFgE,QAAQC,IAAI,4DAGZb,EADiB,mBAEjB6H,EAAe,GAAI,aAGnBtH,WAAUd,EAAAZ,IAAAE,EAAC,SAAAuK,IAAA,IAAAC,EAAAlB,EAAAE,EAAAhB,EAAApB,EAAAsC,EAAAe,EAAA,OAAA3K,IAAAC,EAAA,SAAA2K,GAAA,cAAAA,EAAAhM,EAAAgM,EAAA7M,GAAA,cAAA6M,EAAAhM,EAAA,EAAAgM,EAAA7M,EAAA,EAEiBoM,EAPX,mBAOkCjJ,GAAY,OAArDsI,EAASoB,EAAA7L,EAEfiK,EAAe,IAAK,SAGpB5H,EAAgBoI,EAGhBtB,EAAgBtE,MAAMC,QAAU,OAChCsE,EAAavE,MAAMC,QAAU,OAG7BuG,EAAYhJ,GAENsI,EAAWb,KAAKC,MAAQP,EACxBG,GAAiB,QAANgC,EAAAzJ,SAAM,IAAAyJ,GAAI,QAAJA,EAANA,EAAQlE,UAAE,IAAAkE,OAAA,EAAVA,EAAYhC,WAAY,CAAC,EACpCpB,EAAiBC,IACjBqC,GAAatC,aAAc,EAAdA,EAAgBvB,OAAQ,KAC3CsE,EAAW,KAAD5G,OAAMmG,GAAUnG,OAAGiF,EAASJ,SAAW,MAAK,SAAA7E,OAAQ6G,KAAKC,MAAMb,EAAS,KAAK,MAAM,QAE7FpB,GAAU,EAAKsC,EAAA7M,EAAA,eAAA6M,EAAAhM,EAAA,EAAA+L,EAAAC,EAAA7L,EAGfgD,QAAQY,MAAM,WAAUgI,GACxBN,EAAW,cAAD5G,OAAekH,EAAMjH,SAAW,SAC1CsF,EAAe,EAAG,QAAQ,cAAA4B,EAAA5L,EAAA,KAAAyL,EAAA,iBAE3B,KAAM,OAAA7B,EAAA7K,EAAA,eAAA6K,EAAAhK,EAAA,EAAA+J,EAAAC,EAAA7J,EAIXgD,QAAQY,MAAM,WAAUgG,GAGpBF,EAAeE,EAAMjF,QACnBgF,GAAiB,QAANF,EAAAvH,SAAM,IAAAuH,GAAI,QAAJA,EAANA,EAAQhC,UAAE,IAAAgC,OAAA,EAAVA,EAAYE,WAAY,CAAC,EAEtCC,EAAMjF,QAAQmH,SAAS,MACzBpC,EAAeC,EAASoC,cAAgBnC,EAAMjF,QACrCiF,EAAMjF,QAAQmH,SAAS,OAChCpC,EAAeC,EAASqC,UAAYpC,EAAMjF,QACjCiF,EAAMjF,QAAQmH,SAAS,MAChCpC,EAAeC,EAASwB,aAAevB,EAAMjF,QACpCiF,EAAMjF,QAAQmH,SAAS,QAChCpC,EAAeE,EAAMjF,SAGvB2G,EAAW,KAAD5G,OAAMgF,GAAgB,SAChCO,EAAe,EAAG,QAAQ,OAMe,OANfJ,EAAAhK,EAAA,EAG1BqJ,EAAOc,UAAW,EAGlBiC,EAAS,GAADvH,OAAIvC,EAAW,SAASoH,GAASM,EAAAjK,EAAA,iBAAAiK,EAAA5J,EAAA,KAAAgJ,EAAA,qBAE5CD,EAAAjH,MAAA,KAAAD,UAAA,CAMD,SAASuJ,EAAYrF,GACnB,IAAMqD,EAAiB9F,SAASiB,eAAe,mBACxBjB,SAASiB,eAAe,mBAEhCC,YAAcuB,EAC7BqD,EAAexE,MAAMC,QAAU,OACjC,CA8DA,SAAAoH,IAFC,OAEDA,EAAArK,EAAAZ,IAAAE,EAzDA,SAAAgL,IAAA,IAAAC,EAAAzC,EAAA0C,EAAA,OAAApL,IAAAC,EAAA,SAAAoL,GAAA,cAAAA,EAAAzM,EAAAyM,EAAAtN,GAAA,UACOqD,GAAkBC,EAAgB,CAAAgK,EAAAtN,EAAA,QACH,OAAlCsM,EAAW,aAAc,SAASgB,EAAArM,EAAA,aAAAqM,EAAAzM,EAAA,EAKd,oBAATuE,OAAwBA,KAAK+F,IAAG,CAAAmC,EAAAtN,EAAA,eAAAsN,EAAAtN,EAAA,EAEnCoF,KAAK+F,IAAG,eAAAoC,EAAA1K,EAAAZ,IAAAE,EAAC,SAAAqL,EAAOlC,GAAO,IAAAmC,EAAAlC,EAAAmC,EAAA/C,EAAA,OAAA1I,IAAAC,EAAA,SAAAyL,GAAA,cAAAA,EAAA3N,GAAA,OAGJ,OADjBuL,EAAYD,EAAQ/G,SAASwH,gBACzBC,KAAK,QAAQ2B,EAAA3N,EAAA,EACjBsL,EAAQW,OAAM,WAGhBV,EAAUvE,MAAQuE,EAAUvE,KAAK4G,SAAWxK,EAAawK,OAAM,CAAAD,EAAA3N,EAAA,QACA,OAAjEuL,EAAUsC,WAAWxK,EAAe+B,KAAK0I,eAAeC,SAASJ,EAAA3N,EAAA,EAC3DsL,EAAQW,OAAM,OAAA0B,EAAA3N,EAAA,eAQQ,OALtB0N,EAAgBpC,EAAQ/G,SAASyJ,KAAKC,OAAO7K,EAAa8K,UAAU,EAAG,IAAK,CAChFC,WAAW,EACXC,gBAAgB,KAGJpC,KAAK,SAAS2B,EAAA3N,EAAA,EACtBsL,EAAQW,OAAM,YAEhByB,EAAcW,MAAMjN,OAAS,GAAC,CAAAuM,EAAA3N,EAAA,QAC8C,OAA9E0N,EAAcW,MAAM,GAAGR,WAAWxK,EAAe+B,KAAK0I,eAAeC,SAASJ,EAAA3N,EAAA,EACxEsL,EAAQW,OAAM,OAAA0B,EAAA3N,EAAA,qBAEd,IAAIqE,MAAM,gBAAe,OAKnCE,SAASiB,eAAe,mBAAmBK,MAAMC,QAAU,OAC3DwI,IAEM3D,GAAiB,QAAN8C,EAAAvK,SAAM,IAAAuK,GAAI,QAAJA,EAANA,EAAQhF,UAAE,IAAAgF,OAAA,EAAVA,EAAY9C,WAAY,CAAC,EAC1C2B,EAAW,KAAD5G,OAAMiF,EAAS4D,SAAW,YAAc,QAAQ,cAAAZ,EAAA1M,EAAA,KAAAuM,EAAA,IAC3D,gBAAAgB,GAAA,OAAAjB,EAAAxK,MAAA,KAAAD,UAAA,EAlCa,IAkCZ,OAAAwK,EAAAtN,EAAA,eAGFuE,SAASiB,eAAe,mBAAmBK,MAAMC,QAAU,OAC3DwI,IACM3D,GAAiB,QAANyC,EAAAlK,SAAM,IAAAkK,GAAI,QAAJA,EAANA,EAAQ3E,UAAE,IAAA2E,OAAA,EAAVA,EAAYzC,WAAY,CAAC,EAC1C2B,EAAW,KAAD5G,OAAMiF,EAAS4D,SAAW,WAAU,QAAQ,QAAQ,OAAAjB,EAAAtN,EAAA,eAAAsN,EAAAzM,EAAA,EAAAwM,EAAAC,EAAAtM,EAGhEgD,QAAQY,MAAM,WAAUyI,GACxBf,EAAW,aAAD5G,OAAc2H,EAAM1H,SAAW,SAAS,cAAA2H,EAAArM,EAAA,KAAAkM,EAAA,iBAErDD,EAAAnK,MAAA,KAAAD,UAAA,CAoBD,SAASwL,IACP,IAAMhE,EAAc/F,SAASiB,eAAe,gBACtCiJ,EAAWlK,SAASiB,eAAe,aAEzC,GAAIiJ,EAAU,KAAAC,EACN/D,GAAiB,QAAN+D,EAAAxL,SAAM,IAAAwL,GAAI,QAAJA,EAANA,EAAQjG,UAAE,IAAAiG,OAAA,EAAVA,EAAY/D,WAAY,CAAC,EAC1C8D,EAAShJ,YAAckF,EAASgE,UAAY,+BAC9C,CAEArE,EAAYzE,MAAMC,QAAU,OAG9B,CAcA,SAASmF,EAAe2D,EAAS5H,GAC/B,IAAM6H,EAAetK,SAASiB,eAAe,iBACvCsJ,EAAevK,SAASiB,eAAe,iBAE7CqJ,EAAahJ,MAAMkJ,MAAQ,GAAHrJ,OAAMkJ,EAAO,KACrCE,EAAarJ,YAAcuB,CAC7B,CAKA,SAASkE,IACe3G,SAASiB,eAAe,kBAChCC,YAAc,EAC9B,CAQA,SAASuJ,EAAmBC,GACJ1K,SAASiB,eAAe,kBAChCC,aAAewJ,EAG7B,IAAM7E,EAAe7F,SAASiB,eAAe,iBAC7C4E,EAAa8E,UAAY9E,EAAa+E,YACxC,CAEA,SAMe/C,EAAYgD,EAAAC,GAAA,OAAAC,EAAAvM,MAAC,KAADD,UAAA,CAmP3B,SAAAwM,IAFC,OAEDA,EAAAzM,EAAAZ,IAAAE,EAnPA,SAAAoN,EAA4BvI,EAAMiC,GAAI,IAAAM,EAAAiG,EAAAC,EAAAC,EAAA/F,EAAAgG,EAAAC,EAAAjF,EAAAkF,EAAArF,EAAAsF,EAAApJ,EAAAqJ,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA/O,EAAAC,EAAA+O,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA5B,EAAA6B,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAzP,IAAAC,EAAA,SAAAyP,GAAA,cAAAA,EAAA9Q,EAAA8Q,EAAA3R,GAAA,OAKQ,GALR2R,EAAA9Q,EAAA,EAElCoK,EAAe,GAAI,aAGb1B,EAAiBC,IACF,CAAFmI,EAAA3R,EAAA,cACX,IAAIqE,MAAM,YAADqB,OAAauD,IAAO,OAcrC,GAXMuG,EAAYjG,EAAeqI,IAC7BnC,EAAelG,EAAekG,aAGlCzL,QAAQC,IAAI,0BAADyB,OAA2BuD,IACtCjF,QAAQC,IAAI,eAADyB,OAAgB8J,EAAUqC,MACrC7N,QAAQC,IAAI,aAADyB,OAAc8J,EAAUsC,QACnC9N,QAAQC,IAAI,oBAADyB,OAAqB8J,EAAUuC,cAC1C/N,QAAQC,IAAI,kBAADyB,OAAmB8J,EAAUwC,YACxChO,QAAQC,IAAI,yBAADyB,OAA0BsB,EAAK5F,OAAM,YAI5CmI,EAAeG,gBAAiB,CAAFiI,EAAA3R,EAAA,QAGqC,GAD/D2J,EAAcpF,SAASiB,eAAe,gBACtCmK,EAAoBhG,EAAcA,EAAYlI,MAAMmM,OAAS,GAE3C,CAAF+D,EAAA3R,EAAA,QACuB,MAArC2K,GAAiB,QAANiF,EAAA1M,SAAM,IAAA0M,GAAI,QAAJA,EAANA,EAAQnH,UAAE,IAAAmH,OAAA,EAAVA,EAAYjF,WAAY,CAAC,EACpC,IAAItG,MAAMsG,EAASsH,qBAAuB,cAAa,OAG/DvC,EAAc,GAAHhK,OAAMiK,EAAiB,iBAAAjK,OAAgBsB,GAClDhD,QAAQC,IAAI,0BAADyB,OAA2BiK,IAAqBgC,EAAA3R,EAAA,eAG3D0P,EAAc1I,EAAK,OA2CpB,OAxCDhD,QAAQC,IAAI,qBAADyB,OAAsB+J,EAAavB,UAAU,EAAG,KAAI,QAC/DlK,QAAQC,IAAI,2BAADyB,OAA4BgK,EAAYtO,OAAM,WAMvDyO,EAFEL,EAAUqC,IAAI/E,SAAS,uBAA+C,SAAtB0C,EAAU0C,QAE9C,CACZC,cAAe,YACfC,gBAAiB,GACjBC,MAAO3C,EACP4C,OAAQ,CAAC,GAIG,CACZR,MAAOtC,EAAUsC,MACjBnH,SAAU,CACR,CACE4H,KAAM,SACNtD,QAASQ,GAEX,CACE8C,KAAM,OACNtD,QAASS,IAGbqC,YAAavC,EAAUuC,YACvBS,WAAYhD,EAAUwC,UACtBS,OAAmC,cAA3BjD,EAAUkD,cAItB1O,QAAQC,IAAI,6BACNuG,EAAYM,KAAKC,MAGjB+E,EAAU,CACd,cAAiB,UAAFpK,OAAY8J,EAAUmD,aACrC,eAAgB,oBACjBhB,EAAA3R,EAAA,EAEsB8G,MAAM0I,EAAUqC,IAAK,CAC1Ce,OAAQ,OACR9C,QAASA,EACT9B,KAAM/G,KAAK4L,UAAUhD,KACrB,OAGmF,GAP/EnJ,EAAQiL,EAAA3Q,EAMR+O,EAAejF,KAAKC,MAAQP,EAClCxG,QAAQC,IAAI,6BAADyB,OAA8BgB,EAASY,OAAM,YAAA5B,OAAWqK,EAAY,OAE1ErJ,EAASK,GAAI,CAAF4K,EAAA3R,EAAA,eAAA2R,EAAA3R,EAAA,EACU0G,EAASM,OAAM,OACa,MAD9CgJ,EAAS2B,EAAA3Q,EACfgD,QAAQY,MAAM,yBAADc,OAA0BsK,IACjC,IAAI3L,MAAM,YAADqB,OAAagB,EAASY,OAAM,OAAA5B,OAAMsK,IAAY,OAG/D/E,EAAe,GAAI,aAGbgF,EAASvJ,EAASsH,KAAK8E,YACvB5C,EAAU,IAAI6C,YAChB5C,EAAS,GACTC,EAAS,GACTC,EAAa,EACbC,EAAa,EAEjBtM,QAAQC,IAAI,oCAAoC,OAErC,OAAA0N,EAAA3R,EAAA,EACqBiQ,EAAO+C,OAAM,OAAxB,GAAwBzC,EAAAoB,EAAA3Q,EAAnCQ,EAAI+O,EAAJ/O,KAAMC,EAAK8O,EAAL9O,OAEVD,EAAM,CAAFmQ,EAAA3R,EAAA,gBAAA2R,EAAA1Q,EAAA,cAERoP,IACAC,GAAc7O,EAAML,OAEpBgP,GAAUF,EAAQ+C,OAAOxR,EAAO,CAAEgR,QAAQ,IACpCjC,EAAQJ,EAAO8C,MAAM,MAC3B9C,EAASI,EAAM2C,MAAO1C,EAAA2C,EAEH5C,GAAKmB,EAAA9Q,EAAA,GAAA4P,EAAA1I,IAAA,YAAA2I,EAAAD,EAAAzQ,KAAAwB,KAAE,CAAFmQ,EAAA3R,EAAA,SAAT,KAAJ2Q,EAAID,EAAAjP,OACJ4R,WAAW,UAAW,CAAF1B,EAAA3R,EAAA,SACD,GAEb,YAFP4Q,EAAOD,EAAK2C,MAAM,IAEH,CAAA3B,EAAA3R,EAAA,SAC+F,OAAlHgE,QAAQC,IAAI,iCAADyB,OAAkC2K,EAAU,aAAA3K,OAAY4K,EAAU,qBAAA5K,OAAoByK,EAAO/O,SAAUuQ,EAAA1Q,EAAA,cAQlH,GARkH0Q,EAAA9Q,EAAA,GAK5GgQ,EAAS5J,KAAKC,MAAM0J,GACtB3B,EAAU,GAGO,YAAjB4B,EAAO0C,QAAuB1C,EAAO2C,OAAM,CAAA7B,EAAA3R,EAAA,SAE7CiP,EAAU4B,EAAO2C,OAAO7B,EAAA3R,EAAA,oBACE,oBAAjB6Q,EAAO0C,QAA+B1C,EAAO2C,OAAM,CAAA7B,EAAA3R,EAAA,SAKuB,OAHnFmQ,EAASU,EAAO2C,OAChBtI,IACA8D,EAAmBmB,GACnBlF,EAAesB,KAAKkH,IAAI,GAAMtD,EAAO/O,OAAS4F,EAAK5F,OAAU,GAAI,IAAK,aAAauQ,EAAA1Q,EAAA,cAEzD,gBAAjB4P,EAAO0C,MAAuB,CAAA5B,EAAA3R,EAAA,SAEC,OAAxCgE,QAAQC,IAAI,4BAA4B0N,EAAA1Q,EAAA,WAE/B4P,EAAO6C,SAAW7C,EAAO6C,QAAQ,IAAM7C,EAAO6C,QAAQ,GAAGC,OAAS9C,EAAO6C,QAAQ,GAAGC,MAAM1E,UAEnGA,EAAU4B,EAAO6C,QAAQ,GAAGC,MAAM1E,SACnC,QAEGA,IACFkB,GAAUlB,EACVD,EAAmBC,GAInBhE,EADiBsB,KAAKkH,IAAI,GAAMtD,EAAO/O,OAAS4F,EAAK5F,OAAU,GAAI,IAC1C,cAC1BuQ,EAAA3R,EAAA,iBAAA2R,EAAA9Q,EAAA,GAAA2Q,EAAAG,EAAA3Q,EAGDgD,QAAQc,KAAK,0BAA2B0M,EAAE7L,QAAS,QAASiL,GAAM,QAAAe,EAAA3R,EAAA,iBAAA2R,EAAA3R,EAAA,iBAAA2R,EAAA9Q,EAAA,GAAA4Q,EAAAE,EAAA3Q,EAAAyP,EAAA7Q,EAAA6R,GAAA,eAAAE,EAAA9Q,EAAA,GAAA4P,EAAA7P,IAAA+Q,EAAA/Q,EAAA,YAAA+Q,EAAA3R,EAAA,oBAMtEmQ,EAAOvC,OAAQ,CAAF+D,EAAA3R,EAAA,SAS8F,QAPzG8Q,EAAcX,EAAOvC,QACRyF,WAAW,MAAQvC,EAAY8C,SAAS,MACpD9C,EAAYuC,WAAW,MAAQvC,EAAY8C,SAAS,QACvD9C,EAAcA,EAAYwC,MAAM,GAAI,IAGtCtP,QAAQC,IAAI,kCAADyB,OAAmCoL,EAAY1P,OAAM,WAChE4C,QAAQC,IAAI,yBAADyB,SAA4BsB,EAAK5F,OAAS0P,EAAY1P,QAAU4F,EAAK5F,OAAS,KAAKyS,QAAQ,GAAE,MAAKlC,EAAA1Q,EAAA,EAEtG6P,GAAW,QAE4B,MAA9C9M,QAAQY,MAAM,gCACR,IAAIP,MAAM,YAAW,QAAAsN,EAAA3R,EAAA,iBAa7B,GAb6B2R,EAAA9Q,EAAA,GAAA6Q,EAAAC,EAAA3Q,EAI7BgD,QAAQY,MAAM,qBAAsB8M,EAAM/L,SAC1C3B,QAAQY,MAAM,oBAAqB,CACjCoD,KAAM0J,EAAM1J,KACZrC,QAAS+L,EAAM/L,QACfmO,MAAkB,QAAb/C,EAAEW,EAAMoC,aAAK,IAAA/C,OAAA,EAAXA,EAAamC,MAAM,MAAM,KAG5BvI,GAAiB,QAANqG,EAAA9N,SAAM,IAAA8N,GAAI,QAAJA,EAANA,EAAQvI,UAAE,IAAAuI,OAAA,EAAVA,EAAYrG,WAAY,CAAC,GAGtC+G,EAAM/L,QAAQmH,SAAS,WAAY4E,EAAM/L,QAAQmH,SAAS,mBAAkB,CAAA6E,EAAA3R,EAAA,SAKlC,GAJ5CiL,EAAe,GAAI,aAAa0G,EAAA9Q,EAAA,GAM1B0I,OAFEA,EAAiBC,MAEA,QAAL0H,EAAd3H,EAAgBqI,WAAG,IAAAV,GAAK,QAALA,EAAnBA,EAAqBW,WAAG,IAAAX,IAAxBA,EAA0BpE,SAAS,sBAAqB,CAAA6E,EAAA3R,EAAA,SAEpDuR,EAAUhI,EAAeqI,IAAIC,IAAI9D,QAAQ,qBAAsB,IACrEuD,EAAU,GAAH5L,OAAM6L,EAAO,aAAYI,EAAA3R,EAAA,oBACvBuJ,SAAmB,QAAL4H,EAAd5H,EAAgBqI,WAAG,IAAAT,IAAnBA,EAAqBU,IAAG,CAAAF,EAAA3R,EAAA,SAEjCsR,EAAU/H,EAAeqI,IAAIC,IAAI9D,QAAQ,oBAAqB,WAAW4D,EAAA3R,EAAA,uBAGnE,IAAIqE,MAAM,qBAAoB,eAAAsN,EAAA3R,EAAA,GAGX8G,MAAMwK,EAAS,CACxCsB,OAAQ,MACR9C,QAAS,CACP,cAAiB,UAAFpK,QAAY6D,SAAmB,QAAL6H,EAAd7H,EAAgBqI,WAAG,IAAAR,OAAA,EAAnBA,EAAqBuB,cAAe,OAEjE,QALgB,GAAAhB,EAAA3Q,EAOA+F,GAAI,CAAF4K,EAAA3R,EAAA,eACZ,IAAIqE,MAAM,WAAU,cAItB,IAAIA,MAAM,eAAc,cAAAsN,EAAA9Q,EAAA,GAAA8Q,EAAA3Q,EAExB,IAAIqD,MAAMsG,EAASoC,cAAgB,kBAAiB,QAAA4E,EAAA3R,EAAA,qBAEnD0R,EAAM/L,QAAQmH,SAAS,OAAQ,CAAF6E,EAAA3R,EAAA,eAChC,IAAIqE,MAAMsG,EAASoJ,WAAa,oBAAmB,YAChDrC,EAAM/L,QAAQmH,SAAS,OAAQ,CAAF6E,EAAA3R,EAAA,eAChC,IAAIqE,MAAMsG,EAASqJ,gBAAkB,mBAAkB,cAEvD,IAAI3P,MAAM,GAADqB,OAAIiF,EAASqC,UAAY,UAAS,MAAAtH,OAAKgM,EAAM/L,UAAU,eAAAgM,EAAA1Q,EAAA,KAAAsO,EAAA,iDAG3ExM,MAAA,KAAAD,UAAA,CAOD,SAASwJ,EAAW3G,GAAwB,IAAfsO,EAAInR,UAAA1B,OAAA,QAAA8S,IAAApR,UAAA,GAAAA,UAAA,GAAG,OAC5BwE,EAAS/C,SAASiB,eAAe,UACvC8B,EAAO7B,YAAcE,EACrB2B,EAAO1B,UAAY,UAAHF,OAAauO,GAC7B3M,EAAOzB,MAAMC,QAAU,QAGV,SAATmO,GAA4B,YAATA,GACrBtQ,WAAW,WACT2D,EAAOzB,MAAMC,QAAU,MACzB,EAAG,IAEP,CAOA,SAASoG,EAAalF,GAAM,IAAAmN,EAAAC,EACpBzJ,GAAiB,QAANwJ,EAAAjR,SAAM,IAAAiR,GAAI,QAAJA,EAANA,EAAQ1L,UAAE,IAAA0L,OAAA,EAAVA,EAAYxJ,WAAY,CAAC,EACpC0J,GAAiB,QAAND,EAAAlR,SAAM,IAAAkR,OAAA,EAANA,EAAQC,WAAY,CAAC,EAEtC,IAAKrN,GAAwB,KAAhBA,EAAK4G,OAChB,OAAO,EAIT,IAAM0G,EAAYD,EAASE,eAAiB,IAC5C,GAAIvN,EAAK5F,OAASkT,EAAW,CAC3B,IAAM3O,EAAUgF,EAAS6J,aAAe,aAAJ9O,OAAiB4O,EAAS,SAE9D,OADAhI,EAAW,MAAD5G,OAAOC,GAAW,YACrB,CACT,CAGA,GAA2B,IAAvBqB,EAAK4G,OAAOxM,OAAc,CAC5B,IAAMuE,EAAUgF,EAAS8J,WAAa,eAEtC,OADAnI,EAAW,MAAD5G,OAAOC,GAAW,YACrB,CACT,CAEA,OAAO,CACT,CAOA,SAASsH,EAASyH,EAAQnK,GACxB,IACE,IACMoK,EAAW,CACfC,WAFgB,IAAI9J,MAAO+J,cAG3BH,OAAAA,EACAnK,QAAAA,EACAuK,UAAWC,UAAUD,WAIjBE,EAAO/N,KAAKC,MAAM+N,aAAaC,QAAQ,4BAA8B,MAC3EF,EAAKG,KAAKR,GAGNK,EAAK5T,OAAS,KAChB4T,EAAKI,OAAO,EAAGJ,EAAK5T,OAAS,KAG/B6T,aAAaI,QAAQ,0BAA2BpO,KAAK4L,UAAUmC,GACjE,CAAE,MAAOpQ,GACPZ,QAAQc,KAAK,YAAaF,EAC5B,CACF,EAh5BA,WACyBhB,EAAAb,MAAC,KAADD,UAAA,CA2CzBwS,GAiPAC,OAAOjN,WAAaA,EAkQpBiN,OAAOvM,oBAAsBA,EAC7BuM,OAAOC,eA9FP,WAG6B,OAAAtI,EAAAnK,MAAC,KAADD,UAAA,EA4F7ByS,OAAOE,cAhCP,WAAyB,IAAAC,EACvBnR,SAASiB,eAAe,mBAAmBK,MAAMC,QAAU,OAG3DzC,EAAgB,GAChBD,EAAe,GACfE,EAAmB,KAEnB,IAAMqH,GAAiB,QAAN+K,EAAAxS,SAAM,IAAAwS,GAAI,QAAJA,EAANA,EAAQjN,UAAE,IAAAiN,OAAA,EAAVA,EAAY/K,WAAY,CAAC,EAC1C2B,EAAW,KAAD5G,OAAMiF,EAASgL,WAAa,SAAW,OACnD,C", "sources": ["webpack://word-deepseek-optimizer/./src/taskpane/taskpane.js"], "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n * See LICENSE in the project root for license information.\n */\n\n/* global document, Office, Word */\n\n// 全局变量\nlet config = null; // 配置对象\nlet currentMode = 'optimize'; // 当前选择的模式\nlet originalText = ''; // 保存原始文本\nlet generatedText = ''; // 保存AI生成的文本\nlet currentSelection = null; // 保存当前选择的文本范围\n\n\n\n// 等待Office.js加载完成\nfunction waitForOffice() {\n  return new Promise((resolve) => {\n    if (typeof Office !== 'undefined' && Office.onReady) {\n      resolve();\n    } else {\n      // 等待Office.js加载\n      const checkOffice = () => {\n        if (typeof Office !== 'undefined' && Office.onReady) {\n          resolve();\n        } else {\n          setTimeout(checkOffice, 100);\n        }\n      };\n      setTimeout(checkOffice, 100);\n    }\n  });\n}\n\n// 初始化函数\nasync function initialize() {\n  console.log(\"Starting initialization...\");\n\n  try {\n    // 等待Office.js加载（最多等待5秒）\n    await Promise.race([\n      waitForOffice(),\n      new Promise((_, reject) => setTimeout(() => reject(new Error('Office.js timeout')), 5000))\n    ]);\n\n    console.log(\"Office.js loaded, calling Office.onReady...\");\n\n    Office.onReady((info) => {\n      console.log(\"Office.onReady called, info:\", info);\n\n      // 等待DOM完全加载后初始化应用\n      if (document.readyState === 'loading') {\n        document.addEventListener('DOMContentLoaded', initializeApp);\n      } else {\n        initializeApp();\n      }\n    }).catch(error => {\n      console.error(\"Office.onReady error:\", error);\n      initializeAppFallback();\n    });\n\n  } catch (error) {\n    console.warn(\"Office.js not available or timeout:\", error);\n    initializeAppFallback();\n  }\n}\n\n// 备用初始化（浏览器环境）\nfunction initializeAppFallback() {\n  console.log(\"Fallback: initializing app without Office context...\");\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', initializeApp);\n  } else {\n    initializeApp();\n  }\n}\n\n// 开始初始化\ninitialize();\n\n/**\n * 初始化应用\n */\nasync function initializeApp() {\n  console.log(\"Initializing app...\");\n\n  try {\n    // 检查Office环境\n    console.log(\"Office object:\", typeof Office !== 'undefined' ? Office : 'undefined');\n    console.log(\"Word object:\", typeof Word !== 'undefined' ? Word : 'undefined');\n\n    // 加载配置\n    await loadConfig();\n\n    // 初始化UI\n    initializeUI();\n\n    // 绑定事件\n    bindEvents();\n  } catch (error) {\n    console.error(\"Failed to initialize app:\", error);\n\n    // 显示错误状态\n    try {\n      const statusElement = document.getElementById(\"status\");\n      if (statusElement) {\n        statusElement.textContent = `❌ 应用初始化失败: ${error.message}`;\n        statusElement.className = \"status error\";\n        statusElement.style.display = \"block\";\n      }\n\n      // 隐藏其他元素\n      const elementsToHide = [\"mode-tabs\", \"custom-input-section\", \"instructions\", \"process-button\"];\n      elementsToHide.forEach(id => {\n        const element = document.getElementById(id);\n        if (element) {\n          element.style.display = \"none\";\n        }\n      });\n    } catch (statusError) {\n      console.error(\"Failed to show status:\", statusError);\n    }\n  }\n}\n\n/**\n * 加载配置文件\n */\nasync function loadConfig() {\n  // 尝试多个可能的配置文件路径\n  const possiblePaths = [\n    './conf.json',\n    'conf.json',\n    '../conf.json',\n    '/conf.json'\n  ];\n\n  let lastError = null;\n\n  for (const configPath of possiblePaths) {\n    try {\n      const response = await fetch(configPath);\n\n      if (response.ok) {\n        const configText = await response.text();\n        config = JSON.parse(configText);\n\n        // 验证配置结构\n        if (!config.sections || !Array.isArray(config.sections) || config.sections.length === 0) {\n          throw new Error('Invalid config: sections is empty or not an array');\n        }\n\n        console.log(`✅ Config loaded: ${config.sections.length} sections from ${configPath}`);\n        return; // 成功加载，退出函数\n      } else {\n        lastError = new Error(`HTTP ${response.status} for ${configPath}`);\n      }\n    } catch (error) {\n      lastError = error;\n    }\n  }\n\n  // 如果所有路径都失败了\n  console.error(\"❌ Failed to load config from all paths\");\n  throw new Error(`配置文件加载失败: ${lastError?.message || '所有路径都无法访问'}`);\n}\n\n\n\n/**\n * 初始化UI\n */\nfunction initializeUI() {\n  if (!config) {\n    console.error(\"❌ Config is null or undefined!\");\n    return;\n  }\n\n  if (!config.sections || !Array.isArray(config.sections)) {\n    console.error(\"❌ Config sections is not an array:\", config.sections);\n    return;\n  }\n\n  // 设置标题\n  const header = document.getElementById(\"header\");\n  if (header && config.app) {\n    header.innerHTML = `\n      <h1>${config.app.title}</h1>\n      <p>${config.app.subtitle}</p>\n    `;\n  } else {\n    console.error(\"❌ Header element not found or app config missing\");\n  }\n\n  // 生成模式标签页\n  const modeTabs = document.getElementById(\"mode-tabs\");\n  if (!modeTabs) {\n    console.error(\"❌ mode-tabs element not found\");\n    return;\n  }\n\n  console.log(\"🏷️ Generating tabs for sections:\", config.sections.map(s => s.name));\n  modeTabs.innerHTML = '';\n\n  config.sections.forEach((section, index) => {\n    console.log(`🏷️ Creating tab ${index + 1}: ${section.name} (${section.id})`);\n\n    const tab = document.createElement('button');\n    tab.id = `${section.id}-tab`;\n    tab.className = `mode-tab ${index === 0 ? 'active' : ''}`;\n    tab.textContent = section.name;\n    tab.onclick = () => {\n      switchMode(section.id);\n    };\n    modeTabs.appendChild(tab);\n  });\n\n  console.log(`✅ Created ${config.sections.length} tabs`);\n\n  // 设置默认模式\n  if (config.sections.length > 0) {\n    currentMode = config.sections[0].id;\n    console.log(`🎯 Default mode set to: ${currentMode}`);\n  }\n\n  // 生成使用说明\n  const instructions = document.getElementById(\"instructions\");\n  if (instructions && config.ui?.instructions) {\n    const instructionsConfig = config.ui.instructions;\n    instructions.innerHTML = `\n      <h3>${instructionsConfig.title}</h3>\n      <ol>\n        ${instructionsConfig.steps.map(step => `<li>${step}</li>`).join('')}\n      </ol>\n    `;\n  } else {\n    console.warn(\"⚠️ Instructions element or config not found\");\n    if (instructions) {\n      instructions.style.display = \"none\";\n    }\n  }\n\n  // 更新按钮文本\n  updateButtonText();\n}\n\n/**\n * 绑定所有事件处理器\n */\nfunction bindEvents() {\n  // 绑定处理按钮\n  const processButton = document.getElementById(\"process-button\");\n  if (processButton) {\n    processButton.onclick = processSelectedText;\n  } else {\n    console.error(\"Process button not found\");\n  }\n}\n\n/**\n * 切换处理模式\n * @param {string} mode - 模式ID\n */\nfunction switchMode(mode) {\n  currentMode = mode;\n\n  // 更新标签页样式\n  document.querySelectorAll('.mode-tab').forEach(tab => {\n    tab.classList.remove('active');\n  });\n\n  const activeTab = document.getElementById(`${mode}-tab`);\n  if (activeTab) {\n    activeTab.classList.add('active');\n    console.log(`Added active class to tab: ${activeTab.id}`);\n  }\n\n  // 更新按钮文本\n  updateButtonText();\n\n  // 显示或隐藏自定义输入框\n  const currentSection = getCurrentSection();\n  const customInputSection = document.getElementById('custom-input-section');\n\n  if (currentSection && currentSection.showCustomInput) {\n    customInputSection.style.display = 'block';\n    const customInput = document.getElementById('custom-input');\n    if (customInput && currentSection.customInputPlaceholder) {\n      customInput.placeholder = currentSection.customInputPlaceholder;\n    }\n  } else {\n    customInputSection.style.display = 'none';\n  }\n}\n\n/**\n * 更新按钮文本\n */\nfunction updateButtonText() {\n  const buttonText = document.getElementById('button-text');\n  const currentSection = getCurrentSection();\n\n  if (buttonText && currentSection) {\n    buttonText.textContent = currentSection.buttonText;\n    console.log(`Updated button text to: ${currentSection.buttonText}`);\n  }\n}\n\n/**\n * 获取当前选择的配置段\n */\nfunction getCurrentSection() {\n  if (!config || !config.sections) return null;\n  return config.sections.find(section => section.id === currentMode);\n}\n\n\n\n// 将函数添加到全局作用域\nwindow.switchMode = switchMode;\n\n/**\n * 处理选中的文本（优化或扩写）\n */\nasync function processSelectedText() {\n  console.log(\"processSelectedText called, current mode:\", currentMode);\n\n  const button = document.getElementById(\"process-button\");\n  const progressSection = document.getElementById(\"progress-section\");\n  const streamOutput = document.getElementById(\"stream-output\");\n  const previewSection = document.getElementById(\"preview-section\");\n  const hintSection = document.getElementById(\"hint-section\");\n  const status = document.getElementById(\"status\");\n\n  let success = false;\n  const startTime = Date.now();\n\n  try {\n    // 隐藏之前的结果和提示\n    previewSection.style.display = \"none\";\n    hintSection.style.display = \"none\";\n    status.style.display = \"none\";\n\n    // 显示处理状态\n    button.disabled = true;\n    progressSection.style.display = \"block\";\n    streamOutput.style.display = \"block\";\n\n    // 重置进度和输出\n    updateProgress(0, \"准备中...\");\n    clearStreamOutput();\n\n    // 检查是否在Office环境中\n    if (typeof Word !== 'undefined' && Word.run) {\n      // Office环境中的处理\n      await Word.run(async (context) => {\n        // 获取选中的文本\n        const selection = context.document.getSelection();\n        selection.load(\"text\");\n\n        await context.sync();\n\n        // 验证选中的文本\n        if (!validateText(selection.text)) {\n          const messages = config?.ui?.messages || {};\n          throw new Error(messages.noSelection || \"请先选择有效的文本内容\");\n        }\n\n        // 保存原始文本和选择范围\n        originalText = selection.text;\n        currentSelection = selection;\n\n        updateProgress(20, \"开始处理文本...\");\n\n        // 根据模式调用不同的API\n        const apiResult = await callModelAPI(selection.text, currentMode);\n\n        if (apiResult && apiResult !== selection.text) {\n          updateProgress(100, \"生成完成！\");\n\n          // 保存生成的文本\n          generatedText = apiResult;\n\n          // 隐藏进度和流式输出\n          progressSection.style.display = \"none\";\n          streamOutput.style.display = \"none\";\n\n          // 显示预览\n          showPreview(generatedText);\n\n          const duration = Date.now() - startTime;\n          const messages = config?.ui?.messages || {};\n          const currentSection = getCurrentSection();\n          const actionText = currentSection?.name || '处理';\n          showStatus(`✅ ${actionText}${messages.success || '完成！'}(耗时: ${Math.round(duration/1000)}秒)`, \"info\");\n\n          success = true;\n        } else {\n          updateProgress(100, \"无需处理\");\n          showStatus(\"⚠️ 文本无需处理或AI返回相同内容\", \"warning\");\n          success = true;\n        }\n      });\n    } else {\n      // 浏览器环境中的模拟处理\n      console.log(\"Running in browser environment, simulating processing...\");\n\n      const testText = \"这是一个测试文本，用于演示功能。\";\n      originalText = testText;\n      updateProgress(20, \"开始处理文本...\");\n\n      // 模拟API调用\n      setTimeout(async () => {\n        try {\n          const apiResult = await callModelAPI(testText, currentMode);\n\n          updateProgress(100, \"生成完成！\");\n\n          // 保存生成的文本\n          generatedText = apiResult;\n\n          // 隐藏进度和流式输出\n          progressSection.style.display = \"none\";\n          streamOutput.style.display = \"none\";\n\n          // 显示预览\n          showPreview(generatedText);\n\n          const duration = Date.now() - startTime;\n          const messages = config?.ui?.messages || {};\n          const currentSection = getCurrentSection();\n          const actionText = currentSection?.name || '处理';\n          showStatus(`✅ ${actionText}${messages.success || '完成！'}(耗时: ${Math.round(duration/1000)}秒)`, \"info\");\n\n          success = true;\n\n        } catch (error) {\n          console.error(\"API调用失败:\", error);\n          showStatus(`❌ API调用失败: ${error.message}`, \"error\");\n          updateProgress(0, \"处理失败\");\n        }\n      }, 1000);\n    }\n\n  } catch (error) {\n    console.error(\"处理文本时出错:\", error);\n\n    // 提供更友好的错误消息\n    let errorMessage = error.message;\n    const messages = config?.ui?.messages || {};\n\n    if (error.message.includes(\"网络\")) {\n      errorMessage = messages.networkError || error.message;\n    } else if (error.message.includes(\"API\")) {\n      errorMessage = messages.apiError || error.message;\n    } else if (error.message.includes(\"选择\")) {\n      errorMessage = messages.noSelection || error.message;\n    } else if (error.message.includes(\"配置\")) {\n      errorMessage = error.message;\n    }\n\n    showStatus(`❌ ${errorMessage}`, \"error\");\n    updateProgress(0, \"处理失败\");\n  } finally {\n    // 恢复按钮状态\n    button.disabled = false;\n\n    // 记录使用统计\n    logUsage(`${currentMode}_text`, success);\n  }\n}\n\n/**\n * 显示预览\n * @param {string} text - 要预览的文本\n */\nfunction showPreview(text) {\n  const previewSection = document.getElementById(\"preview-section\");\n  const previewContent = document.getElementById(\"preview-content\");\n\n  previewContent.textContent = text;\n  previewSection.style.display = \"block\";\n}\n\n/**\n * 确认修改 - 将生成的文本应用到Word文档\n */\nasync function confirmChanges() {\n  if (!generatedText || !currentSelection) {\n    showStatus(\"❌ 没有可确认的更改\", \"error\");\n    return;\n  }\n\n  try {\n    if (typeof Word !== 'undefined' && Word.run) {\n      // Office环境中应用更改\n      await Word.run(async (context) => {\n        // 重新获取选择范围（因为可能已经失效）\n        const selection = context.document.getSelection();\n        selection.load(\"text\");\n        await context.sync();\n\n        // 如果当前选择的文本与原始文本匹配，则替换\n        if (selection.text && selection.text.trim() === originalText.trim()) {\n          selection.insertText(generatedText, Word.InsertLocation.replace);\n          await context.sync();\n        } else {\n          // 如果选择已经改变，尝试搜索原始文本并替换\n          const searchResults = context.document.body.search(originalText.substring(0, 50), {\n            matchCase: false,\n            matchWholeWord: false\n          });\n\n          searchResults.load('items');\n          await context.sync();\n\n          if (searchResults.items.length > 0) {\n            searchResults.items[0].insertText(generatedText, Word.InsertLocation.replace);\n            await context.sync();\n          } else {\n            throw new Error(\"无法找到要替换的原始文本\");\n          }\n        }\n\n        // 隐藏预览，显示提示\n        document.getElementById(\"preview-section\").style.display = \"none\";\n        showHint();\n\n        const messages = config?.ui?.messages || {};\n        showStatus(`✅ ${messages.applied || '修改已应用到文档'}`, \"info\");\n      });\n    } else {\n      // 浏览器环境中的模拟\n      document.getElementById(\"preview-section\").style.display = \"none\";\n      showHint();\n      const messages = config?.ui?.messages || {};\n      showStatus(`✅ ${messages.applied || '修改已应用到文档'}（模拟）`, \"info\");\n    }\n  } catch (error) {\n    console.error(\"确认更改时出错:\", error);\n    showStatus(`❌ 应用更改失败: ${error.message}`, \"error\");\n  }\n}\n\n/**\n * 取消修改 - 隐藏预览\n */\nfunction cancelChanges() {\n  document.getElementById(\"preview-section\").style.display = \"none\";\n\n  // 清空保存的数据\n  generatedText = '';\n  originalText = '';\n  currentSelection = null;\n\n  const messages = config?.ui?.messages || {};\n  showStatus(`❌ ${messages.cancelled || '已取消修改'}`, \"info\");\n}\n\n/**\n * 显示提示\n */\nfunction showHint() {\n  const hintSection = document.getElementById(\"hint-section\");\n  const hintText = document.getElementById(\"hint-text\");\n\n  if (hintText) {\n    const messages = config?.ui?.messages || {};\n    hintText.textContent = messages.undoHint || '修改已应用到文档，在文档中按 Ctrl+Z 可撤销此次修改';\n  }\n\n  hintSection.style.display = \"block\";\n\n  // 提示保持显示，不自动消失\n}\n\n// 将函数添加到全局作用域\nwindow.processSelectedText = processSelectedText;\nwindow.confirmChanges = confirmChanges;\nwindow.cancelChanges = cancelChanges;\n\n\n\n/**\n * 更新进度显示\n * @param {number} percent - 进度百分比 (0-100)\n * @param {string} text - 进度文本\n */\nfunction updateProgress(percent, text) {\n  const progressFill = document.getElementById(\"progress-fill\");\n  const progressText = document.getElementById(\"progress-text\");\n\n  progressFill.style.width = `${percent}%`;\n  progressText.textContent = text;\n}\n\n/**\n * 清空流式输出\n */\nfunction clearStreamOutput() {\n  const streamContent = document.getElementById(\"stream-content\");\n  streamContent.textContent = '';\n}\n\n\n\n/**\n * 添加流式输出内容\n * @param {string} content - 要添加的内容\n */\nfunction appendStreamOutput(content) {\n  const streamContent = document.getElementById(\"stream-content\");\n  streamContent.textContent += content;\n\n  // 自动滚动到底部\n  const streamOutput = document.getElementById(\"stream-output\");\n  streamOutput.scrollTop = streamOutput.scrollHeight;\n}\n\n/**\n * 调用模型API处理文本\n * @param {string} text - 要处理的文本\n * @param {string} mode - 处理模式ID\n * @returns {Promise<string>} - 处理后的文本\n */\nasync function callModelAPI(text, mode) {\n  try {\n    updateProgress(30, \"正在生成内容...\");\n\n    // 获取当前模式的配置\n    const currentSection = getCurrentSection();\n    if (!currentSection) {\n      throw new Error(`未找到模式配置: ${mode}`);\n    }\n\n    const apiConfig = currentSection.api;\n    let systemPrompt = currentSection.systemPrompt;\n\n    // API请求调试信息\n    console.log(`🚀 API Request - Mode: ${mode}`);\n    console.log(`🔗 API URL: ${apiConfig.url}`);\n    console.log(`🤖 Model: ${apiConfig.model}`);\n    console.log(`🌡️ Temperature: ${apiConfig.temperature}`);\n    console.log(`📝 Max Tokens: ${apiConfig.maxTokens}`);\n    console.log(`📄 Input Text Length: ${text.length} chars`);\n\n    // 构建用户消息\n    let userMessage;\n    if (currentSection.showCustomInput) {\n      // 自定义模式，获取用户输入的要求\n      const customInput = document.getElementById('custom-input');\n      const customRequirement = customInput ? customInput.value.trim() : '';\n\n      if (!customRequirement) {\n        const messages = config?.ui?.messages || {};\n        throw new Error(messages.customInputRequired || '请输入自定义处理要求');\n      }\n\n      userMessage = `${customRequirement}\\n\\n要处理的文本：\\n${text}`;\n      console.log(`🎯 Custom Requirement: ${customRequirement}`);\n    } else {\n      // 预设模式，直接使用配置的提示词\n      userMessage = text;\n    }\n\n    console.log(`💬 System Prompt: ${systemPrompt.substring(0, 100)}...`);\n    console.log(`👤 User Message Length: ${userMessage.length} chars`);\n\n    // 构建请求体 - 根据API类型选择不同格式\n    let requestBody;\n    if (apiConfig.url.includes('/api/chat-messages') || apiConfig.apiType === 'dify') {\n      // Dify API格式 - 根据真实请求格式\n      requestBody = {\n        response_mode: \"streaming\",\n        conversation_id: \"\",\n        query: userMessage,\n        inputs: {}\n      };\n    } else {\n      // OpenAI API格式\n      requestBody = {\n        model: apiConfig.model,\n        messages: [\n          {\n            role: 'system',\n            content: systemPrompt\n          },\n          {\n            role: 'user',\n            content: userMessage\n          }\n        ],\n        temperature: apiConfig.temperature,\n        max_tokens: apiConfig.maxTokens,\n        stream: apiConfig.responseMode === 'streaming'\n      };\n    }\n\n    console.log(`📤 Sending API request...`);\n    const startTime = Date.now();\n\n    // 构建请求头 - 根据API类型设置\n    const headers = {\n      'Authorization': `Bearer ${apiConfig.bearerToken}`,\n      'Content-Type': 'application/json'\n    };\n\n    const response = await fetch(apiConfig.url, {\n      method: 'POST',\n      headers: headers,\n      body: JSON.stringify(requestBody)\n    });\n\n    const responseTime = Date.now() - startTime;\n    console.log(`📡 API Response - Status: ${response.status}, Time: ${responseTime}ms`);\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(`❌ API Error Response: ${errorText}`);\n      throw new Error(`API请求失败 (${response.status}): ${errorText}`);\n    }\n\n    updateProgress(40, \"正在接收数据...\");\n\n    // 处理流式响应\n    const reader = response.body.getReader();\n    const decoder = new TextDecoder();\n    let result = '';\n    let buffer = '';\n    let chunkCount = 0;\n    let totalBytes = 0;\n\n    console.log(`📥 Starting stream processing...`);\n\n    while (true) {\n      const { done, value } = await reader.read();\n\n      if (done) break;\n\n      chunkCount++;\n      totalBytes += value.length;\n\n      buffer += decoder.decode(value, { stream: true });\n      const lines = buffer.split('\\n');\n      buffer = lines.pop(); // 保留最后一个可能不完整的行\n\n      for (const line of lines) {\n        if (line.startsWith('data: ')) {\n          const data = line.slice(6);\n\n          if (data === '[DONE]') {\n            console.log(`🏁 Stream completed - Chunks: ${chunkCount}, Bytes: ${totalBytes}, Result length: ${result.length}`);\n            break;\n          }\n\n          try {\n            const parsed = JSON.parse(data);\n            let content = '';\n\n            // 检测API类型并解析相应格式\n            if (parsed.event === 'message' && parsed.answer) {\n              // Dify API格式 - message事件\n              content = parsed.answer;\n            } else if (parsed.event === 'message_replace' && parsed.answer) {\n              // Dify API格式 - message_replace事件（替换之前的内容）\n              result = parsed.answer; // 直接替换而不是追加\n              clearStreamOutput();\n              appendStreamOutput(result);\n              updateProgress(Math.min(50 + (result.length / text.length) * 30, 80), \"正在生成内容...\");\n              return; // 跳过后续处理\n            } else if (parsed.event === 'message_end') {\n              // Dify API格式 - 消息结束\n              console.log('✅ Dify message completed');\n              return;\n            } else if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {\n              // OpenAI API格式\n              content = parsed.choices[0].delta.content;\n            }\n\n            if (content) {\n              result += content;\n              appendStreamOutput(content);\n\n              // 更新进度\n              const progress = Math.min(50 + (result.length / text.length) * 30, 80);\n              updateProgress(progress, \"正在生成内容...\");\n            }\n          } catch (e) {\n            // 忽略解析错误，继续处理下一行\n            console.warn('❌ Stream parsing error:', e.message, 'Data:', data);\n          }\n        }\n      }\n    }\n\n    if (result.trim()) {\n      // 移除可能的引号包装\n      let finalResult = result.trim();\n      if ((finalResult.startsWith('\"') && finalResult.endsWith('\"')) ||\n          (finalResult.startsWith(\"'\") && finalResult.endsWith(\"'\"))) {\n        finalResult = finalResult.slice(1, -1);\n      }\n\n      console.log(`✅ API Success - Output length: ${finalResult.length} chars`);\n      console.log(`📊 Compression ratio: ${((text.length - finalResult.length) / text.length * 100).toFixed(1)}%`);\n\n      return finalResult;\n    } else {\n      console.error(`❌ API returned empty content`);\n      throw new Error('API返回空内容');\n    }\n\n  } catch (error) {\n    console.error('❌ API Call Failed:', error.message);\n    console.error('🔍 Error Details:', {\n      name: error.name,\n      message: error.message,\n      stack: error.stack?.split('\\n')[0]\n    });\n\n    const messages = config?.ui?.messages || {};\n\n    // 如果是网络错误，尝试进行连接测试\n    if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {\n      updateProgress(10, \"检测网络连接...\");\n\n      try {\n        // 简单的连接测试 - 使用当前配置的API地址\n        const currentSection = getCurrentSection();\n        let testUrl;\n        if (currentSection?.api?.url?.includes('/api/chat-messages')) {\n          // Dify API - 使用基础URL进行测试\n          const baseUrl = currentSection.api.url.replace('/api/chat-messages', '');\n          testUrl = `${baseUrl}/api/ping`;\n        } else if (currentSection?.api?.url) {\n          // OpenAI API\n          testUrl = currentSection.api.url.replace('/chat/completions', '/models');\n        } else {\n          // 如果没有配置，跳过连接测试\n          throw new Error('无法进行连接测试：未找到API配置');\n        }\n\n        const testResponse = await fetch(testUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${currentSection?.api?.bearerToken || ''}`\n          }\n        });\n\n        if (!testResponse.ok) {\n          throw new Error('服务器连接失败');\n        }\n\n        // 连接正常，重新抛出原始错误\n        throw new Error('API调用失败，请重试');\n      } catch (testError) {\n        throw new Error(messages.networkError || '网络连接失败，请检查网络连接');\n      }\n    } else if (error.message.includes('401')) {\n      throw new Error(messages.authError || 'API认证失败，请检查API密钥');\n    } else if (error.message.includes('429')) {\n      throw new Error(messages.rateLimitError || 'API调用频率过高，请稍后再试');\n    } else {\n      throw new Error(`${messages.apiError || 'API调用失败'}: ${error.message}`);\n    }\n  }\n}\n\n/**\n * 显示状态消息\n * @param {string} message - 消息内容\n * @param {string} type - 消息类型 (info, warning, error)\n */\nfunction showStatus(message, type = \"info\") {\n  const status = document.getElementById(\"status\");\n  status.textContent = message;\n  status.className = `status ${type}`;\n  status.style.display = \"block\";\n\n  // 如果是成功或警告消息，3秒后自动隐藏\n  if (type === \"info\" || type === \"warning\") {\n    setTimeout(() => {\n      status.style.display = \"none\";\n    }, 3000);\n  }\n}\n\n/**\n * 验证文本内容\n * @param {string} text - 要验证的文本\n * @returns {boolean} - 是否有效\n */\nfunction validateText(text) {\n  const messages = config?.ui?.messages || {};\n  const settings = config?.settings || {};\n\n  if (!text || text.trim() === \"\") {\n    return false;\n  }\n\n  // 检查文本长度（从配置读取最大长度）\n  const maxLength = settings.maxTextLength || 5000;\n  if (text.length > maxLength) {\n    const message = messages.textTooLong || `文本过长，请选择少于${maxLength}字符的文本`;\n    showStatus(`⚠️ ${message}`, \"warning\");\n    return false;\n  }\n\n  // 检查是否只包含空白字符\n  if (text.trim().length === 0) {\n    const message = messages.emptyText || \"请选择包含实际内容的文本\";\n    showStatus(`⚠️ ${message}`, \"warning\");\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * 记录使用统计\n * @param {string} action - 操作类型\n * @param {boolean} success - 是否成功\n */\nfunction logUsage(action, success) {\n  try {\n    const timestamp = new Date().toISOString();\n    const logEntry = {\n      timestamp,\n      action,\n      success,\n      userAgent: navigator.userAgent\n    };\n\n    // 存储到本地存储（可选）\n    const logs = JSON.parse(localStorage.getItem('deepseek-optimizer-logs') || '[]');\n    logs.push(logEntry);\n\n    // 只保留最近100条记录\n    if (logs.length > 100) {\n      logs.splice(0, logs.length - 100);\n    }\n\n    localStorage.setItem('deepseek-optimizer-logs', JSON.stringify(logs));\n  } catch (error) {\n    console.warn('无法记录使用统计:', error);\n  }\n}\n"], "names": ["e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "bind", "length", "l", "TypeError", "call", "done", "value", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "config", "currentMode", "originalText", "generatedText", "currentSelection", "waitForOffice", "Office", "onReady", "checkOffice", "setTimeout", "_initialize", "_callee", "_t", "_context", "console", "log", "race", "_", "reject", "Error", "info", "document", "readyState", "addEventListener", "initializeApp", "catch", "error", "initializeAppFallback", "warn", "_initializeApp", "_callee2", "statusElement", "_t2", "_context2", "Word", "loadConfig", "initializeUI", "bindEvents", "getElementById", "textContent", "concat", "message", "className", "style", "display", "for<PERSON>ach", "id", "element", "statusError", "_loadConfig", "_callee3", "_lastError", "lastError", "_i", "_possiblePaths", "config<PERSON><PERSON>", "response", "configText", "_t3", "_context3", "fetch", "ok", "text", "JSON", "parse", "sections", "Array", "isArray", "status", "_config$ui", "header", "app", "innerHTML", "title", "subtitle", "modeTabs", "map", "s", "name", "section", "index", "tab", "createElement", "onclick", "switchMode", "append<PERSON><PERSON><PERSON>", "instructions", "ui", "instructionsConfig", "steps", "step", "join", "updateButtonText", "processButton", "processSelectedText", "mode", "querySelectorAll", "classList", "remove", "activeTab", "add", "currentSection", "getCurrentSection", "customInputSection", "showCustomInput", "customInput", "customInputPlaceholder", "placeholder", "buttonText", "find", "_processSelectedText", "_callee6", "button", "progressSection", "streamOutput", "previewSection", "hintSection", "success", "startTime", "_config8", "errorMessage", "messages", "_t5", "_context6", "Date", "now", "disabled", "updateProgress", "clearStreamOutput", "run", "_ref", "_callee4", "context", "selection", "_config5", "apiResult", "_config6", "duration", "_messages", "actionText", "_context4", "getSelection", "load", "sync", "validateText", "noSelection", "callModelAPI", "showPreview", "showStatus", "Math", "round", "_x3", "_callee5", "_config7", "_t4", "_context5", "includes", "networkError", "apiError", "logUsage", "_confirm<PERSON><PERSON>es", "_callee8", "_config0", "_t6", "_context8", "_ref3", "_callee7", "_config9", "searchResults", "_context7", "trim", "insertText", "InsertLocation", "replace", "body", "search", "substring", "matchCase", "matchWholeWord", "items", "showHint", "applied", "_x4", "hintText", "_config2", "undoHint", "percent", "progressFill", "progressText", "width", "appendStreamOutput", "content", "scrollTop", "scrollHeight", "_x", "_x2", "_callModelAPI", "_callee9", "apiConfig", "systemPrompt", "userMessage", "customRequirement", "_config1", "requestBody", "headers", "responseTime", "errorText", "reader", "decoder", "result", "buffer", "chunkCount", "totalBytes", "_yield$reader$read", "lines", "_iterator", "_step", "line", "data", "parsed", "finalResult", "_error$stack", "_config10", "_messages2", "_currentSection$api", "_currentSection$api2", "_currentSection$api3", "_currentSection", "testUrl", "baseUrl", "_t7", "_t8", "_t9", "_context9", "api", "url", "model", "temperature", "maxTokens", "customInputRequired", "apiType", "response_mode", "conversation_id", "query", "inputs", "role", "max_tokens", "stream", "responseMode", "bearerToken", "method", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "read", "decode", "split", "pop", "_createForOfIteratorHelper", "startsWith", "slice", "event", "answer", "min", "choices", "delta", "endsWith", "toFixed", "stack", "authError", "rateLimitError", "type", "undefined", "_config3", "_config4", "settings", "max<PERSON><PERSON><PERSON>", "maxText<PERSON>ength", "textTooLong", "emptyText", "action", "logEntry", "timestamp", "toISOString", "userAgent", "navigator", "logs", "localStorage", "getItem", "push", "splice", "setItem", "initialize", "window", "confirm<PERSON><PERSON><PERSON>", "cancelChanges", "_config", "cancelled"], "sourceRoot": ""}